
"use client"
import React, { useState, useEffect } from 'react'
import { BsArrowRight } from "react-icons/bs";
import Image from 'next/image';
import Link from 'next/link';
import { getAllProjects, transformProjectToCard, ProjectCardData } from '@/api/projects/projects_api';

export default function OurLatestProject() {
  const [projects, setProjects] = useState<ProjectCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const projectsData = await getAllProjects();
        const transformedProjects = projectsData.map(transformProjectToCard);
        // Only show first 4 projects for homepage
        setProjects(transformedProjects.slice(0, 4));
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to load projects');
        console.error('Error loading projects:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  if (loading) {
    return (
      <div className="text-white min-h-screen py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
        <div className="text-center">
          <div className="text-[24px] font-bold">Loading Latest Projects...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-white min-h-screen py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className=" text-white min-h-screen py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px]">
      <div className="w-full px-10">
        {/* Header Section */}
        <div className="flex justify-between items-center mb-[48px] md:mb-[64px] lg:mb-[80px]">
          <div>
            <h2 className="text-[78px] md:text-[78px] lg:text-[78px] font-bold font-satoshi leading-tight">
             <span className="p-4 bg-[#2B2B2B] mb-8 rounded-tl-xl rounded-tr-xl rounded-br-xl">Our Latest</span>
              <br />
              <span className="px-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl">Works</span>
            </h2>
          </div>
          <Link href="/projects">
            <button className="border border-white text-white text-[24px] px-[24px] py-[12px] rounded-full font-medium hover:bg-[#ffffff] hover:text-black transition-colors">
              Explore More
            </button>
          </Link>
        </div>

        {/* Projects Grid */}
        <div className="space-y-[24px] md:space-y-[32px] lg:space-y-[40px]">
          {projects.map((project) => (
            <div
              key={project.id}
              className="flex flex-col lg:flex-row gap-[24px] md:gap-[32px] lg:gap-[48px] lg:justify-between items-center w-full"
            >
              {/* Left Content Card - 70% width */}
              <Link href={`/project-details/${project.id}/${project.slug}`} className="w-full lg:w-[60%]">
                <div className={`bg-[#2B2B2B] hover:bg-[#363636] rounded-[24px] cursor-pointer p-6 h-[300px] md:h-[350px] lg:h-[400px] flex flex-row justify-between gap-20 relative overflow-hidden w-full transition-all duration-300 hover:scale-105`}>
                {/* Project Serial Number */}
                <div className="text-[18px] md:text-[20px] lg:text-[24px] font-bold text-white/80 mb-[16px]">
                  {project.serialNo.toString().padStart(2, '0')}
                  {/* serial number from API */}
                </div>

                {/* Project Title */}
                <div className="flex-1 flex flex-col justify-between py-7">
                    <div>
                        <h3 className='text-white text-[20px] opacity-20'>{project.category}</h3>
                  <h3 className="text-[24px] md:text-[48px] lg:text-[48px] font-bold text-white leading-tight mb-[8px]">
                    {project.title}
                    {/* project title from API */}
                  </h3>
                    </div>
                 <div className='flex flex-row justify-between items-center'>
                     <p className="text-[24px] md:text-[18px] text-white/90 leading-relaxed max-w-[420px]">
                    {project.description}
                    {/* project description from API */}
                  </p>
                   <div className="w-[80.9px] h-[80.9px] border p-3 border-white text-white text-[3rem] hover:text-[#FF640F] hover:border-[#FF640F] rounded-full flex items-center justify-center backdrop-blur-sm">
                   <BsArrowRight />

                  </div>

                 </div>
                </div>

              </div>
              </Link>

              {/* Right Image - 30% width */}
              <div className="relative h-[300px] md:h-[350px] lg:h-[400px] rounded-[50px] overflow-hidden w-full lg:w-[30%]">
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  {/* Placeholder for now - you can replace with actual images */}
                  <div className="text-center">
                    <div className="w-full h-full  rounded-lg mx-auto mb-4 flex items-center justify-center">
                      <Image
                      src={project.bigImageUrl}
                      alt="Project Image"
                      width={480}
                      height={448}
                      className="w-full h-full object-cover"
                    />
                    {/* bigImageUrl from API */}
                    </div>
                   
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
