"use client";

import { useParams } from "next/navigation";
import { Projectdata } from "@/data/project";
import Restaurant from "@/app/(pages)/work/resturant";
import Ecommerce from "@/app/(pages)/work/ecommerce";
import Portfolio from "@/app/(pages)/work/portfolio";
import Idcindia from "@/app/(pages)/work/Idc";
import MaiCourt from "@/app/(pages)/work/Maicourt";
import Idcons from "@/app/(pages)/work/Idcons";
import Mehendi from "@/app/(pages)/work/Mehendi";

const WorkDetail = () => {
  const { gallaryId } = useParams();
  console.log(gallaryId);

  try {
    if (!gallaryId) {
      return <div>Loading...</div>;
    }

    // Find the project based on the id from the URL
    const gallarydetails = Projectdata.find(
      (project) => project.id === gallaryId
    );

    if (!gallarydetails) {
      return <div>Project not found</div>;
    } else {
      return (
        <div className="">
          {gallarydetails.id === "1" && <Ecommerce />}
          {gallarydetails.id === "2" && <Portfolio />}
          {gallarydetails.id === "3" && <Restaurant />}
          {gallarydetails.id === "4" && <Idcindia />}
          {gallarydetails.id === "5" && <MaiCourt/>}
          {gallarydetails.id === "6" && <Idcons/>}
          {gallarydetails.id === "7" && <Mehendi/>}

        </div>
      );
    }
  } catch (error) {
    console.error(error);
  }
};

export default WorkDetail;
