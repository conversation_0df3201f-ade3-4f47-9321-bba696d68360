'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { getAllServices, transformServiceToCard } from '@/api/services/services_api';

// Utility function to encode string to URL-safe format
const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]/g, '')
  );
};

interface ServiceDetailsSidebarProps {
  currentServiceId: string; // Changed to string since we're using _id
}

interface Card {
  id: string; // Changed to string since we're using _id
  title: string;
  description: string;
  icon: string;
  iconBg: string;
  _id: string;
}

export default function ServiceDetailsSidebar({ currentServiceId }: ServiceDetailsSidebarProps) {
  const [services, setServices] = useState<Card[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const servicesData = await getAllServices();
        const transformedCards = servicesData.map(transformServiceToCard);
        setServices(transformedCards);
      } catch (err) {
        console.error('Error loading services:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="bg-[#2B2B2B] rounded-2xl p-5">
          <div className="space-y-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-12 bg-gray-700 rounded-lg animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Service Categories */}
      <div className="bg-[#2B2B2B] rounded-2xl p-5  ">


        {/* Service List */}
        <div className="space-y-3">
          {services.map((service) => (
            <Link
              key={service.id}
              href={`/service-details/${service.id}/${encodeUrlString(service.title)}`}
              className={`block p-3 text-[20px] leading-[40px] rounded-lg transition-all duration-200 ${
                service.id === currentServiceId
                  ? 'bg-white text-black'
                  : 'hover:bg-white text-white opacity-75 hover:text-black'
              }`}
            >
              <div className="flex items-center space-x-3">
               
                <span className="text-sm font-medium line-clamp-2">
                  {service.title.split(' ').slice(0, 5).join(' ')}.
                </span> 
              </div>
            </Link>
          ))}
        </div>
      </div>

    

      {/* Contact Section */}
      <div className="border border-[#2B2B2B] rounded-2xl p-6 flex flex-col justify-center text-center gap-7 items-center">
       <div>
         <h3 className="text-[32px] leading-[35px] tracking-[-1px] font-semibold text-white mb-4">Any Questions?</h3>
        <p className="text-[32px] leading-[35px] tracking-[-1px] ">Let's Talk</p>
       </div>
        <Link 
          href="/contact"
          className=" w-full border border-white  hover:bg-white hover:text-black font-semibold py-3  text-[20px] rounded-[30px] text-center  transition-colors"
        >
          Let's Talk
        </Link>
      </div>
    </div>
  );
}
