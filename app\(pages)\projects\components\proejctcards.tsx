


"use client"
import React, { useState, useEffect } from 'react'
import Image from 'next/image';
import { BsArrowRight } from "react-icons/bs";
import Link from 'next/link';
import { getAllProjects, transformProjectToCard, ProjectCardData } from '@/api/projects/projects_api';

export const ProjectCards = () => {
  const [projects, setProjects] = useState<ProjectCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const projectsData = await getAllProjects();
        const transformedProjects = projectsData.map(transformProjectToCard);
        setProjects(transformedProjects);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to load projects');
        console.error('Error loading projects:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  if (loading) {
    return (
      <div className="w-full text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
        <div className="text-center">
          <div className="text-[24px] font-bold">Loading Projects...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
      <div className="max-w-[95%] px-10 ">
        {/* Header Section */}
        <div className="w-full flex justify-between items-center mb-[48px] md:mb-[64px] lg:mb-[80px]">
          <div className='w-[50%] text-[72px] leading-[78px] tracking-[-2px] font-bold'>
           Our Projects
          </div>
          <div className='w-[50%] text-[24px] leading-[36px]'>
            With built-in features and customizable extensions, editors offer comprehensive solutions for every user’s preferences.
          </div>
        </div>

        {/* Projects Grid */}
        <div className="space-y-[24px] md:space-y-[32px] lg:space-y-[40px]">
          {projects.map((project) => (
            <div
              key={project.id}
              
              className="flex flex-col lg:flex-row gap-[24px] md:gap-[32px] lg:gap-[48px] items-center w-full"
            >
              {/* Left Content Card - 60% width */}
              <Link href={`/project-details/${project.id}/${project.slug}`} className="w-full lg:w-[60%]">
                <div className={`bg-[#2B2B2B] hover:bg-[#363636] group rounded-[24px] cursor-pointer p-6 h-[300px] md:h-[350px] lg:h-[400px] flex flex-row justify-between gap-20 relative overflow-hidden w-full transition-all duration-300 hover:scale-105`}>
                {/* Project Serial Number */}
                <div className="text-[18px] md:text-[20px] lg:text-[24px] font-bold group-hover:text-[#FF640F] text-white/80 mb-[16px]">
                  {project.serialNo.toString().padStart(2, '0')}
                  {/* Serial number from API */}
                </div>

                {/* Project Title */}
                <div className="flex-1 flex flex-col justify-between py-7">
                    <div>
                        <h3 className='text-white text-[20px] opacity-20'>{project.category}</h3>
                  <h3 className="text-[24px] md:text-[48px] lg:text-[48px] font-bold text-white leading-tight mb-[8px]">
                    {project.title}
                    {/* project title from API */}
                  </h3>
                    </div>
                 <div className='flex flex-row justify-between items-center'>
                     <p className="text-[24px] md:text-[18px] text-white/90 leading-relaxed max-w-[420px]">
                    {project.description}
                    {/* description from API */}
                  </p>
                   <div className="w-[80.9px] h-[80.9px] border p-3 border-white group-hover:border-[#FF640F]  group-hover:text-[#FF640F] text-[30px] rounded-full flex items-center justify-center backdrop-blur-sm">

                   <BsArrowRight />
                  </div>

                 </div>
                </div>

              </div>
              </Link>

              {/* Right Image - 40% width */}
              <div className="relative h-[300px] md:h-[350px] lg:h-[400px] rounded-[24px] overflow-hidden bg-gray-800 w-full lg:w-[40%]">
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <div className="w-full h-full rounded-lg mx-auto mb-4 flex items-center justify-center">
                      <Image
                      src={project.bigImageUrl}
                      alt="Project Image"
                      width={480}
                      height={448}
                      className="w-[30rem] h-[28rem] object-cover"
                    />
                    {/* bigImageUrl from API */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
