'use client';

import React from 'react';

interface FooterSectionProps {
  title: string;
  links: string[];
}

const FooterSection = ({ title, links }: FooterSectionProps) => (
  <div>
    <h4 className="mb-2 font-semibold">{title}</h4>
    <ul className="space-y-1 font-medium">
      {links.map((link) => (
        <li key={link} className="hover:text-orange-500 cursor-pointer">{link}</li>
      ))}
    </ul>
  </div>
);

export default FooterSection;