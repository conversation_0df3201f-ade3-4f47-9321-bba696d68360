"use client"

import React, { useState, useEffect } from "react";
import { BsArrowRight } from "react-icons/bs";
import { BsArrowLeft } from "react-icons/bs";
import Image from "next/image";
import Link from "next/link";
import { getAllServices, transformServiceToCard } from "@/api/services/services_api";

// Utility function to encode string to URL-safe format
const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]/g, '')
  );
};

// Generate service URL
const generateServiceUrl = (id: string, title: string): string => {
  const encodedTitle = encodeUrlString(title);
  return `/service-details/${id}/${encodedTitle}`;
};

// Define the type for the card data
interface Card {
  id: string;
  title: string;
  description: string;
  icon: string;
  iconBg: string;
  _id: string;
}

export default function OurServices() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [services, setServices] = useState<Card[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const cardsPerView = 4; // Show 4 cards at a time like in Figma
  const maxIndex = Math.max(0, services.length - cardsPerView);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const servicesData = await getAllServices();
        const transformedCards = servicesData.map(transformServiceToCard);
        setServices(transformedCards);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to load services');
        console.error('Error loading services:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  const nextSlide = () => {
    setCurrentIndex(prev => prev >= maxIndex ? 0 : prev + 1);
  };

  const prevSlide = () => {
    setCurrentIndex(prev => prev <= 0 ? maxIndex : prev - 1);
  };

  if (loading) {
    return (
      <div className="w-full py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
        <div className="text-center mb-12">
          <h2 className="text-[78px] leading-[78px] font-bold text-white">Our Services</h2>
          <p className="text-white opacity-[70%] text-[24px] leading-[36px] mt-2">
            Loading our amazing services...
          </p>
        </div>
        <div className="flex gap-6 w-full max-w-[88%] justify-center">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="w-1/4 min-w-[400px] h-[490px] rounded-3xl bg-gradient-to-br from-[#2B2B2B] to-[#1B1B1B] animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
        <div className="text-center mb-12">
          <h2 className="text-[78px] leading-[78px] font-bold text-white">Our Services</h2>
          <p className="text-red-400 text-[24px] leading-[36px] mt-2">
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-10 pl-14  min-h-screen">
      <div className="max-w-full ">
        {/* Header Section */}
        <div className="flex justify-between items-center mb-12 px-4">
          {/* Title with curved background */}
          <div className="relative">
            <div className=" rounded-2xl px-8 py-6 inline-block">
              <h2 className="text-[78px] md:text-[78px] font-bold text-white leading-tight ">
                <span className="p-1 bg-[#2B2B2B] mb-4 rounded-tl-xl rounded-tr-xl rounded-br-xl">Our provided</span><br /><span className="p-2 bg-[#2B2B2B] mt-6 rounded-bl-xl rounded-br-xl">services</span>
              </h2>
            </div>
          </div>

          {/* Navigation Arrows */}
          <div className="flex gap-4 mt-6">
            <button
              onClick={prevSlide}
              className="w-[100px] h-[100px] hover:bg-white text-white text-[2rem] hover:text-black border border-white rounded-full flex items-center justify-center  transition-colors"
            >
              <BsArrowLeft/>             
            </button>
            <button
              onClick={nextSlide}
              className="w-[100px] h-[100px] border-2 text-white hover:text-black hover:bg-white text-[2rem] border-white rounded-full flex items-center justify-center hover:border-white transition-colors"
            >
              <BsArrowRight />             
            </button>
          </div>
        </div>

        {/* Services Grid with Slider */}
        <div className="overflow-hidden">
          <div
            className="flex  transition-transform duration-500 ease-in-out gap-6"
            style={{ transform: `translateX(-${currentIndex * (100 / cardsPerView)}%)` }}
          >
            {services.map((service: Card) => (
              <Link
                key={service.id}
                href={generateServiceUrl(service.id, service.title)}
                className="flex-shrink-0 w-1/4 min-w-[400px]"
              >
                <div className={`h-[490px] bg-[#2B2B2B] hover:bg-[#FFD365] hover:text-black  text-white rounded-3xl p-12 flex flex-col justify-between hover:scale-105 transition-transform duration-300`}>
                  {/* Icon Section */}
                  <div
                    className="w-[77px] h-[77px] rounded-full flex items-center justify-center mb-6"
                    style={{ backgroundColor: service.iconBg }}
                  >
                    <Image
                      src={service.icon}
                      alt={service.title}
                      width={38}
                      height={35}
                      className="object-contain"
                    />
                  </div>

                  {/* Content Section */}
                  <div className="flex flex-col">
                 <h3 className="font-satoshi font-black text-[26px] mb-4 leading-tight">
                      {service.title.split(' ').slice(0, 5).join(' ') +'...'}
                    </h3>
                    <p className={` opacity-[70%]  text-[18px] leading-relaxed `}>
                      {service.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}