import axios from "axios";

// Base URL configuration
const BASE_URL = "http://localhost:8001/api";

// Create axios instance with base configuration
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    }
});

// Team member interface based on API response
export interface TeamMember {
    _id: string;
    name: string;
    jobCategory: string;
    imageUrl: string;
    status: string;
    socialMedia: {
        website: string | null;
        facebook: string | null;
        linkedin: string | null;
        instagram: string | null;
        github: string | null;
    };
    createdAt: string;
    updatedAt: string;
    id: number;
    __v: number;
}

// Pagination interface
export interface Pagination {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
}

// API Response interface for get all team members
export interface TeamResponse {
    status: boolean;
    code: number;
    message: string;
    data: {
        teamMembers: TeamMember[];
        pagination: Pagination;
    };
}

// Get all team members with founders sorted to top and reversed order (latest first)
export const getAllTeamMembers = async (): Promise<TeamMember[]> => {
    try {
        const response = await apiClient.get<TeamResponse>('/team');
        const teamMembers = response.data.data.teamMembers;

        // First reverse the array to show latest added members first
        const reversedTeamMembers = [...teamMembers].reverse();

        // Then sort team members: Founders first, then others (maintaining reversed order within each group)
        const sortedTeamMembers = reversedTeamMembers.sort((a, b) => {
            const aIsFounder = a.jobCategory.toLowerCase().includes('founder');
            const bIsFounder = b.jobCategory.toLowerCase().includes('founder');

            // If both are founders or both are not founders, maintain reversed order
            if (aIsFounder === bIsFounder) {
                return 0;
            }

            // If a is founder and b is not, a comes first
            if (aIsFounder && !bIsFounder) {
                return -1;
            }

            // If b is founder and a is not, b comes first
            return 1;
        });

        return sortedTeamMembers;
    } catch (error: any) {
        console.error('Error fetching team members:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch team members');
    }
};

// Transform API team data for component use
export interface TeamCardData {
    id: string;
    name: string;
    role: string;
    image: string;
    socialMedia: {
        facebook: string | null;
        twitter: string | null; // Using website as twitter for compatibility
        linkedin: string | null;
        instagram: string | null;
        github: string | null;
    };
}

export const transformTeamMemberToCard = (member: TeamMember): TeamCardData => {
    return {
        id: member._id,
        name: member.name,
        role: member.jobCategory,
        image: member.imageUrl,
        socialMedia: {
            facebook: member.socialMedia.instagram, // Using instagram as facebook for icon compatibility
            twitter: member.socialMedia.website,    // Using website as twitter for icon compatibility
            linkedin: member.socialMedia.linkedin,
            instagram: member.socialMedia.instagram,
            github: member.socialMedia.github
        }
    };
};
