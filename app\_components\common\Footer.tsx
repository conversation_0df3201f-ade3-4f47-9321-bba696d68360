import React, { useState } from "react";
import Link from "next/link";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaLinkedinIn } from "react-icons/fa";
import { BsArrowRight } from "react-icons/bs";


const Footer: React.FC = () => {
  const [email, setEmail] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log("Newsletter subscription:", email);
    setEmail("");
  };

  return (
    <footer className=" flex flex-col justify-center items-center text-white mt-14 mb-4">
      <div className="max-w-[80%] ">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-12">

          {/* Newsletter Subscription Section - Increased width (5 columns out of 12) */}
          <div className="lg:col-span-5">
            <h2 className="text-[64px] lg:text-[64px] font-bold leading-tight mb-8">
              Subscribe to our newsletter
            </h2>

            <form onSubmit={handleSubmit} className="relative">
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  className="w-full bg-white text-black px-6 py-4 rounded-[10px] outline-none pr-16 text-base placeholder-gray-500 "
                  required
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-black p-3 rounded-full   transition-colors duration-300"
                  aria-label="Subscribe"
                >
                  <BsArrowRight className="text-4xl"/>
                </button>
              </div>
            </form>
          </div>

          {/* Navigation Links - Column 1 (3 columns out of 12) */}
          <div className="lg:col-span-2">
            <ul className="space-y-4">
              <li>
                <Link
                  href="/about"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  About
                </Link>
              </li>
              <li>
                <Link
                  href="/gallarydetail"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Work Gallery
                </Link>
              </li>
              <li>
                <Link
                  href="/pricing"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Pricing
                </Link>
              </li>
              <li>
                <Link
                  href="/blog"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Blog
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Navigation Links - Column 2 - Decreased width (2 columns out of 12) */}
          <div className="lg:col-span-2">
            <ul className="space-y-4">
              <li>
                <Link
                  href="/faq"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Faq's
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-white hover:text-white transition-colors duration-300 text-base"
                >
                  Terms
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Information (2 columns out of 12) */}
          <div className="lg:col-span-3">
            <div className="space-y-4">
              <div>
                <p className="text-white text-base leading-relaxed">
                  82/3 Patel Nagar, Near Metro Station,
                </p>
                <p className="text-white text-base leading-relaxed">
                  New Delhi — 110008, India
                </p>
              </div>

              <div>
                <p className="text-white text-xl font-semibold">
                  (+91) ************
                </p>
              </div>

               <div>
                <p className="text-white text-xl font-semibold">
                  <EMAIL>
                </p>
              </div>

              {/* Social Media Icons */}
              <div className="flex space-x-8 pt-4">
                <Link
                  href="https://www.facebook.com/agkraft"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-3 rounded-full hover:bg-white transition-colors duration-300"
                >
                  <FaFacebookF className="text-lg" />
                </Link>
                <Link
                  href="https://twitter.com/agkraft"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-3 rounded-full hover:bg-white transition-colors duration-300"
                >
                  <FaTwitter className="text-lg" />
                </Link>
                <Link
                  href="https://www.linkedin.com/company/agkraft"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-3 rounded-full hover:bg-white transition-colors duration-300"
                >
                  <FaLinkedinIn className="text-lg" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-800 mt-16 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex space-x-8 text-gray-400 text-sm">
              <span className="hover:text-white transition-colors duration-300 cursor-pointer">AGKRaft</span>
              <span className="hover:text-white transition-colors duration-300 cursor-pointer">Partners</span>
              <span className="hover:text-white transition-colors duration-300 cursor-pointer">Careers</span>
            </div>

            <div className="text-gray-400 text-sm">
              ©2025 AGKRaft. All Right Reserved
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;