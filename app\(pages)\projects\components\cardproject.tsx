
"use client";
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface ProjectCardProps {
  id: string;
  title: string;
  description1: string;
  description2: string;
  image: any;
  tech: string;
  design: string;
  height: string;
  lightColor: string;
}

export const CardProject = ({ project }: { project: ProjectCardProps }) => {
  return (
    <Link href={`/gallarydetail/${project.id}`}>
      <div className={`bg-[#1B1B1B] hover:bg-[#2B2B2B] rounded-[24px] cursor-pointer p-6 ${project.height} flex flex-col justify-between transition-all duration-300 hover:scale-105 group`}>
        {/* Project Image */}
        <div className="relative w-full h-[200px] rounded-[16px] overflow-hidden mb-4">
          <Image
            src={project.image}
            alt={project.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
          />
          {/* Gradient overlay */}
          <div
            className="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-300"
            style={{ background: project.lightColor }}
          />
        </div>

        {/* Project Content */}
        <div className="flex-1 flex flex-col justify-between">
          {/* Tech Badge */}
          <div className="flex items-center gap-2 mb-3">
            <span className="bg-[#f8c33e] text-black text-sm px-3 py-1 rounded-full font-medium">
              {project.tech}
            </span>
            <span className="text-white/60 text-sm">
              {project.design}
            </span>
          </div>

          {/* Title */}
          <h3 className="text-white text-xl font-bold mb-3 group-hover:text-[#f8c33e] transition-colors duration-300">
            {project.title}
          </h3>

          {/* Description */}
          <p className="text-white/80 text-sm leading-relaxed line-clamp-3">
            {project.description1}
          </p>

          {/* View Details Button */}
          <div className="mt-4 flex items-center justify-between">
            <span className="text-[#f8c33e] text-sm font-medium group-hover:underline">
              View Details
            </span>
            <div className="w-8 h-8 border border-white/30 rounded-full flex items-center justify-center group-hover:border-[#f8c33e] group-hover:bg-[#f8c33e] transition-all duration-300">
              <svg
                className="w-4 h-4 text-white group-hover:text-black transition-colors duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};
