'use client';

import { useState } from 'react';
import Image from 'next/image';
import gulshan from '@/public/new assests/news icons/heroicons/aboutus/gulshan.png';
import kunal from '@/public/new assests/news icons/heroicons/aboutus/kunal.png';
import aryan from '@/public/new assests/news icons/heroicons/aboutus/aryan.png';
import doublemark from "@/public/new assests/news icons/heroicons/doubleaxlimationmark.svg"
import { IoIosArrowRoundForward } from "react-icons/io";
import { IoIosArrowRoundBack } from "react-icons/io";

export default function SecondSlider() {
  const [currentSlide, setCurrentSlide] = useState(40); // Start from middle for infinite scroll (8 testimonials * 5)

  const testimonials = [
    {
      quote: 'Game-changer! Boosted efficiency, simplified tasks, and saved time. Highly recommended!',
      author: '<PERSON>',
      image: gulshan,
      country: 'USA',
    },
    {
      quote: 'One should not hesitate to ask for the unlikely as they might think.',
      author: '<PERSON>',
      image: kunal,
      country: 'RUSSIA',
    },
    {
      quote: 'Quick solutions coupled with great performance—a recommendation that’s unequivocal!',
      author: '<PERSON>',
      image: aryan,
      country: 'GERMANY',
    },
    {
      quote: 'One should not ask for the unlikely as they might think.',
      author: '<PERSON> <PERSON>',
      image: gulshan,
      country: 'INDIA',
    },
    {
      quote: 'Exceptional service and outstanding results. Would definitely recommend to others!',
      author: 'John Smith',
      image: kunal,
      country: 'JAPAN',
    },
    {
      quote: 'Professional team with great attention to detail. Exceeded our expectations!',
      author: 'Sarah <PERSON>',
      image: aryan,
      country: 'KOREA',
    },
    {
      quote: 'Innovative solutions and timely delivery. A pleasure to work with!',
      author: 'David Brown',
      image: gulshan,
      country: 'FRANCE',
    },
    {
      quote: 'Top-notch quality and excellent customer service. Highly satisfied!',
      author: 'Lisa Davis',
      image: kunal,
      country: 'CALOFONIYA',
    },
  ];

  const cardWidth = 25; // Each card takes 25% width (100/4)

  const nextSlide = () => {
    setCurrentSlide((prev) => prev + 1);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => prev - 1);
  };





  return (
    <div className="w-full  text-white py-16 px-8">
      <div className="max-full mx-auto">
        {/* Header with title and navigation */}
        <div className="flex justify-between items-center mb-12 mx-20">
          <h2 className="text-[65px] md:text-[72px] leading-[72px] font-bold">What Developer Say</h2>

          {/* Navigation arrows */}
          <div className="flex space-x-2">
            <button
              onClick={prevSlide}
              className="w-[100.32px] h-[100.32px] hover:bg-white hover:text-black text-white rounded-full flex items-center justify-center border border-white transition-colors duration-200"
            >
              <IoIosArrowRoundBack className='text-[50px]' />
            </button>
            <button
              onClick={nextSlide}
              className="w-[100.32px] h-[100.32px] hover:bg-orange-500 text-white rounded-full flex items-center justify-center hover:border-none border border-white transition-colors duration-200"
            >
              <IoIosArrowRoundForward className='text-[50px]' />
            </button>
          </div>
        </div>

        {/* Testimonial cards container with smooth animation */}
        <div className="relative overflow-hidden ml-[4rem]">
          {/* Extra div for opacity effect on right side */}
          <div className="absolute top-0 right-0 w-32 h-full  bg-gradient-to-l from-black to-transparent z-10 pointer-events-none"></div>

          {/* Sliding container */}
          <div
            className="flex transition-transform   duration-700 ease-in-out"
            style={{
              transform: `translateX(-${currentSlide * cardWidth}%)`,
            }}
          >
            {/* Create very large array for truly infinite loop */}
            {Array.from({ length: testimonials.length * 20 }, (_, index) => {
              const testimonial = testimonials[index % testimonials.length];
              return (
                <div
                  key={index}
                  className="flex-shrink-0 px-3"
                  style={{ width: `${cardWidth}%` }}
                >
                  <div className="border border-white border-opacity-15 rounded-[30px] p-10 h-full flex flex-col gap-y-10 justify-between min-h-[280px] transform transition-all duration-500">
                    {/* Quote marks */}
                    <div className="mb-4">
                      <Image src={doublemark} alt="Double comma" width={24} height={24} className="w-[35.83px] h-[23.24px]" />
                    </div>

                    {/* Testimonial text */}
                    <p className="text-gray-300 text-[28px] leading-[40px] mb-6 flex-grow">
                      {testimonial.quote}
                    </p>

                    {/* Author info */}
                    <div className="flex items-center">
                      <Image
                        src={testimonial.image}
                        alt={testimonial.author}
                        className="rounded-full mr-3 w-[63px] h-[63px]"
                      />
                      <div className='flex flex-col'>
                        <span className="text-white font-medium text-[24px]">{testimonial.author}</span>
                      <span className="text-white/50 text-[20px] ml-2">{testimonial.country}</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}