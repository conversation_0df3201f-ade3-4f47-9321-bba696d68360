"use client"
import Image from "next/image";
import profilechat from "@/public/new assests/news icons/heroicons/homeimage/profilechat.png";
import computercode from "@/public/new assests/news icons/heroicons/homeimage/computercode.png";    
import tabletpencil from "@/public/new assests/news icons/heroicons/homeimage/tabletpencil.png";    
export default function ContactUsHome() {
  return (
    <div className="text-white mt-10 flex flex-col items-center justify-center  py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px]">
      <div className="w-full max-w-[90%]">
        {/* Single Contact Card Container */}
        <div className="bg-[#2B2B2B] rounded-[32px] p-4 md:p-8 lg:p-10 relative overflow-hidden">

          {/* Center Content */}
          <div className="text-center">
            {/* Main Heading */}
            <h2 className="text-[70px] md:text-[80px] lg:text-[80px] font-bold text-white mb-4 leading-[80px] tracking-[-4px]">
              Want to Chat
              <Image
                src={profilechat}
                alt="Profile Chat"
                width={40}
                height={40}
                className="inline-block w-[80px] h-[60px] md:w-[80px] md:h-[60px] ml-2 mb-2"
              />
              ? Feel free to
              <br />
              Contact our Team.
            </h2>

            {/* Subtitle */}
            <div className="flex flex-row justify-between items-center">
              <div>
                <Image
                  src={tabletpencil}
                  alt="Tablet Pencil"
                  className="w-[159.2px] h-[102.68px] md:w-[159.2px] md:h-[102.68px]"
                />
              </div>

              <div>
                <p className="text-white/80 text-[28px] md:text-[28px] mb-8 leading-[28px]">
                  If you have anything in mind just contact us with our expert.
                </p>

                {/* CTA Button */}
                <button className="border border-white hover:bg-white hover:text-black text-white px-8 py-4 rounded-full text-[20px] font-medium transition-all duration-300">
                  Let's Get Started
                </button>
              </div>

              <div>
                <Image
                  src={computercode}
                  alt="Computer Code"
                  className="w-[261px] h-[233px] md:w-[261px] md:h-[233px] "
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
