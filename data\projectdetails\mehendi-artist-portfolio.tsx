// Mehendi Artist Portfolio Project Details
import img7 from "@/public/new assests/proejct/mehendi.png"

export const mehendiArtistPortfolioDetails = {
  id: "05",
  uniqueId: "mehendi-artist-portfolio",
  title: "Mehendi Artist Portfolio Website",
  category: "Portfolio Design",
  overview: {
    description: "Created a beautiful and functional portfolio website for a professional mehendi artist. The design celebrates traditional art while incorporating modern web design principles to showcase the artist's work and attract new clients.",
    description2: "Balancing traditional aesthetic elements with modern web design while ensuring the portfolio effectively showcases intricate mehendi designs and attracts potential clients.",
    
  },
  projectDetails: {
    client: "Professional Mehendi Artist",
    duration: "2 Months",
    categories: "Ecommerce Platform",
    website: "www.google.com",
  },
  processSteps: [
    {
      step: "02",
      title: "Process & Challenge",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.",
      description2: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.",
      points: [
        "Creating high-impact image galleries with zoom functionality",
        "Implementing booking and contact systems for client inquiries",
        "Designing for mobile-first viewing since most clients browse on phones",
        "Balancing traditional aesthetics with modern usability"
      ]
    },
    {
      step: "03",
      title: "Summary",
      description: "The portfolio website successfully increased the artist's client bookings by 200% and established a strong online presence. The design effectively showcases the intricate beauty of mehendi art while providing easy navigation for potential clients."
    }
  ],
  images: {
    hero: img7,
    gallery: [img7, img7, img7]
  },
  technologies: ["WordPress", "PHP", "MySQL", "JavaScript"],
  features: [
    "Image Gallery with Lightbox",
    "Online Booking System",
    "Client Testimonials",
    "Social Media Integration",
    "Contact Forms"
  ],
  nextProject: {
    id: "06",
    title: "Ecommerce Site Design",
    slug: "ecommerce-site-design"
  },
  previousProject: {
    id: "04",
    title: "Website User Interface",
    slug: "website-user-interface"
  }
};
