// OLD CONTACT FORM API - COMMENTED OUT
// import axios from "axios";

// const apiClient = axios.create({
//     baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
// });

// export interface FormData {
//     first_name: string;
//     last_name: string;
//     email: string;
//     phone: string;
//     message: string;
//     country?: string;
//     city?: string;
//     services: string;
// }

// export const submitContactForm = async (formData: FormData) => {
//     try {
//         const response = await apiClient.post("/contact-form/submit", formData);
//         return response.data;
//     } catch (error: any) {
//         throw new Error(error.response?.data?.message || "Form submission failed");
//     }
// };
