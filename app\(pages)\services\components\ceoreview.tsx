"use client"
import imageceo from "@/public/new assests/news icons/heroicons/aboutus/gulshan.png";
import doubemark from "@/public/new assests/news icons/heroicons/doubleaxlimationmark.svg";
import Image from "next/image";

export const CeoReview = () => {
  return (
   <div className="w-full bg-[#0C0C0C] py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
     <div className="w-[85%]  p-6 rounded-[40px]  flex items-center gap-4">
      <div className="w-[30%] flex items-center justify-center">
        <div className="w-[298px] h-[298px]  rounded-full mr-6">
        <Image src={imageceo} alt="CEO Gulshan" className="object-cover w-full h-full rounded-full" />
      </div>
      </div>
      <div className="flex flex-col w-[65%] text-white py-6">
        <p className="text-[48px] leading-[67px]">
          “For any growing business, our web development service is a must-have — crafted by professionals, built for performance.”
        </p>
       <div className="flex flex-row justify-between items-center">
         <p className="text-white font-bold mt-2 leading-[33px] text-[20px]">James Bond. 
            <span className="text-white leading-[33px]  opacity-[50%]">
                CEO & Head of Retro AI
            </span>
            </p>
             <div className="ml-4 bg-black w-[67px] h-[67px] rounded-full flex items-center justify-center">
        <Image src={doubemark} alt="Double comma" width={24} height={24} className="w-[35.83px] h-[23.24px]" />
      </div>
       </div>
      </div>
     
    </div>
   </div>
  );
};