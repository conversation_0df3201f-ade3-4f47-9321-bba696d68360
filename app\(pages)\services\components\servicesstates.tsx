"use client"

import { useEffect, useRef } from "react";

const ServicesStates = () => {
  const counters = [
    { value: 1500, label: "Projects Deployed Globally", suffix: "+" },
    { value: 4.5, label: "Faster Website Load Times", suffix: "X" },
    { value: 900, label: "Increase in Client Conversions", suffix: "%" },
  ];

  const animateCounter = (element: HTMLSpanElement, target: number, duration: number) => {
    let start = 0;
    const range = target;
    const increment = target / (duration / 16); // Assuming 16ms per frame
    const timer = setInterval(() => {
      start += increment;
      if (start >= target) {
        clearInterval(timer);
        element.textContent = target.toFixed(target % 1 === 0 ? 0 : 1) + (element.dataset.suffix || "");
      } else {
        element.textContent = Math.floor(start).toFixed(target % 1 === 0 ? 0 : 1) + (element.dataset.suffix || "");
      }
    }, 16);
  };

  useEffect(() => {
    counters.forEach((counter, index) => {
      const element = document.getElementById(`counter-${index}`);
      if (element) {
        if (counter.suffix) {
          element.dataset.suffix = counter.suffix;
        }
        animateCounter(element, counter.value, 2000); // 2000ms duration for animation
      }
    });
  }, []);

  return (
    <div className=" flex bg-[#0C0C0C] justify-around items-center text-white py-6 px-6">
      {counters.map((counter, index) => (
        <div key={index} className="text-center">
          <span
            id={`counter-${index}`}
            className="text-[100px]  transition-all duration-1000"
            data-suffix={counter.suffix || ""}
          >
            0
          </span>
          <p className="text-[24px] text-white opacity-[60%] leading-[34px] ">{counter.label}</p>
        </div>
      ))}
    </div>
  );
};

export default ServicesStates;