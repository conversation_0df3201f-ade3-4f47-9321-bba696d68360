"use client";

import React, { useState, useEffect } from 'react';
import { FiMessageSquare, FiUser, FiMail, FiSend, FiClock, FiCheckCircle, FiAlertCircle } from 'react-icons/fi';
import { submitComment, getApprovedComments, Comment, formatCommentDate } from '@/api/comments/comments_api';

interface BlogCommentsProps {
  blogId?: string;
  blogTitle?: string;
}

interface CommentFormData {
  name: string;
  email: string;
  comment: string;
}

const BlogComments: React.FC<BlogCommentsProps> = ({ blogId, blogTitle }) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CommentFormData>({
    name: '',
    email: '',
    comment: ''
  });
  const [formErrors, setFormErrors] = useState<Partial<CommentFormData>>({});

  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await getApprovedComments({ limit: 50 });
      setComments(response.data);
    } catch (err) {
      console.error('Error fetching comments:', err);
      setError('Failed to load comments');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const errors: Partial<CommentFormData> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.comment.trim()) {
      errors.comment = 'Comment is required';
    } else if (formData.comment.trim().length < 10) {
      errors.comment = 'Comment must be at least 10 characters';
    } else if (formData.comment.trim().length > 1000) {
      errors.comment = 'Comment must be less than 1000 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field when user starts typing
    if (formErrors[name as keyof CommentFormData]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSubmitting(true);
      setError(null);

      await submitComment({
        name: formData.name.trim(),
        email: formData.email.trim(),
        comment: formData.comment.trim()
      });

      // Reset form
      setFormData({ name: '', email: '', comment: '' });
      setShowSuccess(true);
      
      // Hide success message after 5 seconds
      setTimeout(() => setShowSuccess(false), 5000);

    } catch (err: any) {
      console.error('Error submitting comment:', err);
      setError(err.response?.data?.message || 'Failed to submit comment. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="bg-[#0B0A0A] text-white py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-2 flex items-center">
            <FiMessageSquare className="mr-3 h-6 w-6 text-orange-500" />
            Comments ({comments.length})
          </h2>
          <p className="text-gray-400">
            Share your thoughts about this article. Your comment will be reviewed before publishing.
          </p>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mb-6 bg-green-900/50 border border-green-500 rounded-lg p-4">
            <div className="flex items-center">
              <FiCheckCircle className="h-5 w-5 text-green-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-green-400">Comment Submitted Successfully!</h3>
                <p className="text-sm text-green-300 mt-1">
                  Thank you for your comment. It will be reviewed and published soon.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center">
              <FiAlertCircle className="h-5 w-5 text-red-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-red-400">Error</h3>
                <p className="text-sm text-red-300 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Comment Form */}
        <div className="bg-gray-900/50 rounded-lg p-6 mb-8 border border-gray-800">
          <h3 className="text-lg font-semibold text-white mb-4">Leave a Comment</h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Name Field */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                  <FiUser className="inline mr-2 h-4 w-4" />
                  Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors ${
                    formErrors.name ? 'border-red-500' : 'border-gray-700'
                  }`}
                  placeholder="Your full name"
                  disabled={submitting}
                />
                {formErrors.name && (
                  <p className="mt-1 text-sm text-red-400">{formErrors.name}</p>
                )}
              </div>

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  <FiMail className="inline mr-2 h-4 w-4" />
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors ${
                    formErrors.email ? 'border-red-500' : 'border-gray-700'
                  }`}
                  placeholder="<EMAIL>"
                  disabled={submitting}
                />
                {formErrors.email && (
                  <p className="mt-1 text-sm text-red-400">{formErrors.email}</p>
                )}
              </div>
            </div>

            {/* Comment Field */}
            <div>
              <label htmlFor="comment" className="block text-sm font-medium text-gray-300 mb-2">
                <FiMessageSquare className="inline mr-2 h-4 w-4" />
                Comment *
              </label>
              <textarea
                id="comment"
                name="comment"
                rows={4}
                value={formData.comment}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors resize-vertical ${
                  formErrors.comment ? 'border-red-500' : 'border-gray-700'
                }`}
                placeholder="Share your thoughts about this article..."
                disabled={submitting}
              />
              <div className="flex justify-between items-center mt-1">
                {formErrors.comment ? (
                  <p className="text-sm text-red-400">{formErrors.comment}</p>
                ) : (
                  <p className="text-sm text-gray-500">
                    {formData.comment.length}/1000 characters
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={submitting}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FiSend className="mr-2 h-4 w-4" />
                    Submit Comment
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Existing Comments */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-6">
            {comments.length > 0 ? 'Recent Comments' : 'No Comments Yet'}
          </h3>
          
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <FiMessageSquare className="mx-auto h-12 w-12 text-gray-600 mb-4" />
              <p>Be the first to leave a comment!</p>
            </div>
          ) : (
            <div className="space-y-6">
              {comments.map((comment) => (
                <div key={comment.id} className="bg-gray-900/30 rounded-lg p-6 border border-gray-800">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {comment.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="ml-3">
                        <h4 className="text-white font-medium">{comment.name}</h4>
                        <div className="flex items-center text-sm text-gray-400">
                          <FiClock className="mr-1 h-3 w-3" />
                          {formatCommentDate(comment.createdAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-300 leading-relaxed">{comment.comment}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogComments;
