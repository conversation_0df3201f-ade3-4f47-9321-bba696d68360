"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState } from "react";

const schema = z.object({
  fullName: z.string().min(3, "Full name must be at least 3 characters."),
  email: z.string().email("Invalid email address."),
  phone: z.string().regex(/^\d{10}$/, "Phone number must be 10 digits."),
  position: z.string().min(1, "Please select a position."),
  expectedSalary: z.string().min(1, "Please enter expected salary."),
  workType: z.string().min(1, "Select work type."),
  qualification: z.string().min(1, "Enter qualification."),
  experience: z.number().min(0, "Experience cannot be negative."),
  skills: z.array(z.string()).min(1, "Select at least one skill."),
  resume: z
    .any()
    .refine((file) => file.length > 0, "Resume is required.")
    .refine((file) => file[0]?.size <= 2 * 1024 * 1024, "Max file size is 2MB."),
  linkedin: z.string().url("Enter a valid LinkedIn URL."),
  coverLetter: z.string().optional(),
  declaration: z.literal(true, { errorMap: () => ({ message: "You must accept the declaration." }) }),
});

const HiringForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({ resolver: zodResolver(schema) });
  
  const [submitted, setSubmitted] = useState(false);

  const onSubmit = (data : any) => {
    console.log("Form Data:", data);
    setSubmitted(true);
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-gray-900 text-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4">AGKraft Hiring Form</h2>
      {submitted ? (
        <p className="text-green-500">Form submitted successfully!</p>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <input {...register("fullName")} placeholder="Full Name" className="input" />
          <p className="text-red-500">{errors.fullName?.message?.toString()}</p>
          
          <input {...register("email")} placeholder="Email" className="input" />
          <p className="text-red-500">{errors.email?.message?.toString()}</p>
          
          <input {...register("phone")} placeholder="Phone Number" className="input" />
          <p className="text-red-500">{errors.phone?.message?.toString()}</p>
          
          <select {...register("position")} className="input">
            <option value="">Select Position</option>
            <option value="Frontend Developer">Frontend Developer</option>
            <option value="Backend Developer">Backend Developer</option>
          </select>
          <p className="text-red-500">{errors.position?.message?.toString()}</p>
          
          <input {...register("expectedSalary")} placeholder="Expected Salary" className="input" />
          <p className="text-red-500">{errors.expectedSalary?.message?.toString()}</p>
          
          <input {...register("linkedin")} placeholder="LinkedIn URL" className="input" />
          <p className="text-red-500">{errors.linkedin?.message?.toString()}</p>
          
          <input type="file" {...register("resume")} className="input" />
          <p className="text-red-500">{errors.resume?.message?.toString()}</p>
          
          <label className="flex items-center">
            <input type="checkbox" {...register("declaration")} />
            <span className="ml-2">I confirm all information is accurate.</span>
          </label>
          <p className="text-red-500">{errors.declaration?.message?.toString()}</p>
          
          <button type="submit" className="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded text-white">Submit</button>
        </form>
      )}
    </div>
  );
}

export default HiringForm
