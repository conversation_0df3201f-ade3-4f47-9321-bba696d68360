"use client"
import curvearrow from "@/public/new assests/news icons/heroicons/homeimage/curvearrow.svg"
import tablemeet from "@/public/new assests/news icons/heroicons/homeimage/tablemeet.png"
import meeting from "@/public/new assests/news icons/heroicons/homeimage/meeting.png"
import ImageCardsWithBadge from "../common/ImageCardsWithBadge";

export default function HomeAbout() {
  return (
    <div className="text-white min-h-screen py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] mt-[32px] md:mt-[48px] lg:mt-[64px]">
      <div className="max-w-full mx-[16px] md:mx-[24px] lg:mx-[32px] relative">

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-[32px] md:gap-[48px] lg:gap-[64px] xl:gap-[80px] items-start">

          {/* Left Side - Main Heading */}
          <div className="relative">
            {/* Main Heading */}
          <h1 className="text-[50px] md:text-[50px] lg:text-[50px] xl:text-[78px] font-bold leading-tight mb-[16px]">
              <span className="bg-[#2B2B2B]   z-10 rounded-lg block ">Transforming Idea</span>
              <span className="bg-[#2B2B2B]  z-5 rounded-b-lg">into Success.</span>
            </h1>
          </div>

          {/* Right Side - About Us Content */}
          <div className="mt-[32px] md:mt-[48px] lg:mt-0">
            <div className="mb-[32px] flex flex-col gap-10">
              <h2 className="text-[20px] font-medium text-white opacity-30 tracking-wider uppercase mb-[16px]">
                ABOUT US
              </h2>
              <div className="w-full">
                <p className="text-white text-[18px] md:text-[20px] lg:text-[24px] xl:text-[32px] font-thin leading-relaxed mb-[14px]">
                  Tortor porttitor tortor ut vitae commodo et. Et morbi at felis vestibulum pulvinar libero ut netus neque. Eget quis condimentum diam et lectus.
                </p>
                <p className="text-white text-[18px] md:text-[20px] lg:text-[24px] xl:text-[32px] leading-relaxed ">
                  Tortor porttitor tortor ut vitae commodo et. Et morbi at felisfortune eget libero netus
                </p>
              </div>

              {/* More About Us Button */}
              <button className="w-[207px]  border border-white text-white px-[16px] md:px-[24px] py-[8px] md:py-[12px] rounded-full hover:bg-white hover:text-black transition-all duration-300 text-[24px] md:text-[24px]">
                More About us
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Image Cards with Decorative Elements */}
        <ImageCardsWithBadge
          leftImage={tablemeet}
          rightImage={meeting}
          leftImageAlt="Team Meeting"
          rightImageAlt="Business Meeting"
          curveArrowImage={curvearrow}
          badgeText="BEST AWARD WINNER"
          badgeLetter="A"
        />

      </div>
    </div>
  )
}
