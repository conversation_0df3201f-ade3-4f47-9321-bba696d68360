"use client"
import React from 'react'
import Image from 'next/image'

// Import team member images (you can replace these with actual team photos)
import aryan from "@/public/assest/aryan.png"
import gulshan from "@/public/assest/gulshan.png"
import kunal from "@/public/assest/kunal.png"

// icoms
import diamond from "@/public/new assests/news icons/heroicons/diamond.svg"
import twotriangle from "@/public/new assests/news icons/heroicons/twotriangle.svg"
import rectanglecategory from "@/public/new assests/news icons/heroicons/rectangle category.png"

export default function HeroSectionCards() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 xl:grid-cols-5 xl:gap-8 lg:gap-2 md:gap-4 px-10 max-w-full items-end">

      {/* Background Tech Image Card - First on mobile, last on desktop */}
      <div className="relative rounded-[20px] overflow-hidden xl:w-full lg:w-full md:w-full w-full xl:h-[564px] lg:h-[404px]  md:h-[455px]  h-[455px]   order-1 xl:order-1">
        <div className="absolute inset-0 bg-[url('/assest/Tech.jpg')] bg-cover bg-center opacity-60"></div>
        <div className="relative z-10 p-6 h-full flex items-end">
         
        </div>
      </div>

      {/* Card 2: Team & Happy Clients */}
      <div className="bg-[#B5FF9D] rounded-[50px] xl:p-7 lg:p-3 p-7 text-black xl:h-[455px]  xl:w-full lg:w-full md:w-full w-full  lg:h-[354px]  md:h-[455px]   h-[455px] flex flex-col justify-between order-3 xl:order-2">
        <div className="flex items-center gap-2 mb-4">
          <div className="flex xl:-space-x-4 lg:-space-x-4 md:-space-x-4 -space-x-4">
            <Image
              src={aryan}
              alt="Team member"
              width={70}
              height={70}
              className="rounded-full border-[6px] border-[#B5FF9D]  xl:w-[80.19px] xl:h-[80px] lg:w-[60.19px] lg:h-[60px] md:w-[80.19px] md:h-[80px]  w-[80.19px] h-[80px]"
            />
            <Image
              src={gulshan}
              alt="Team member"
              width={70}
              height={70}
              className="rounded-full border-[6px] border-[#B5FF9D]  xl:w-[80.19px] xl:h-[80px] lg:w-[60.19px] lg:h-[60px]  md:w-[80.19px] md:h-[80px]  w-[80.19px] h-[80px]"
            />
            <Image
              src={kunal}
              alt="Team member"
              width={70}
              height={70}
              className="rounded-full border-[6px] border-[#B5FF9D]  xl:w-[80.19px] xl:h-[80px] lg:w-[60.19px] lg:h-[60px] md:w-[80.19px] md:h-[80px]  w-[80.19px] h-[80px]"
            />
             <div className="xl:w-[80.19px] xl:h-[80px] lg:w-[60.19px] lg:h-[60px]  md:w-[80.19px] md:h-[80px]  w-[80.19px] h-[80px] bg-[#65D83F] rounded-full flex items-center border-[6px] border-[#B5FF9D] justify-center">
            <span className="text-[#202020] font-bold text-[42px]">+</span>
          </div>
          </div>
         
        </div>
        <div className='flex flex-col  justify-start items-start '>
          <p className="text-[22px] opacity-[60%] font-medium ">Happy Client</p>
          <h3 className="xl:text-[82px] lg:text-[55px] md:text-[82px] text-[82px]  font-bold">1.7mi+</h3>
        </div>
      </div>

      {/* Card 3: Successful Projects */}
      <div className="bg-[#2A2A2A] rounded-3xl p-6 text-white  xl:h-[353px]  xl:w-full lg:w-full md:w-full w-full  lg:h-[254px] md:h-[455px]   h-[455px]  flex flex-col justify-center order-4 xl:order-3">
        <div className="flex flex-col items-center justify-center xl:gap-10 lg:gap-5 md:gap-10 -gap-5 ">
          <div className="w-[53px] h-[53px] bg-[#3A3A3A] rounded-full flex items-center justify-center">
            <Image src={diamond} alt="Icon" width={24} height={24} className='w-[25.5px] h-[20.4px]' />
          </div>
           <div className="text-center">
          <h3 className="xl:text-[70px] lg:text-[55px] md:text-[70px] text-[70px] font-bold mb-2">1.3k</h3>
          <p className="text-[24px] text-white opacity-70">Successful Projects</p>
        </div>

        </div>
       
      </div>

      {/* Card 4: Testimonial */}
      <div className="bg-[#FFA0F9] rounded-3xl xl:p-8 lg:p-3 md:p-8 p-8 text-white xl:h-[455px]  xl:w-full lg:w-full md:w-full w-full  lg:h-[354px]   md:h-[455px]   h-[455px]   flex flex-col justify-between order-5 xl:order-4">
        <div className="flex justify-start ">
          <div className="w-[51px] h-[51px] bg-[#EE76E6] rounded-full flex items-center justify-center">
            <Image src={twotriangle} alt="Icon" width={24} height={24} className='w-[14.25px] h-[15.4px]' />
          </div>
        </div>
        <div>
          <p className="xl:text-[24px] lg:text-[18px] md:text-[24px] text-[24px] xl:p-2   text-black leading-[32px] ">
            “Efficient, knowledgeable, & smooth experience. Highly recommended”
          </p>
         
        </div>
        <div>
           <p className="text-[20px]">
            <span className="font-semibold text-[#000000]">Harsul Arrora,</span><span className='text-black opacity-[40%] text-[18px]'> CEO Macgence</span>
          </p>
        </div>
      </div>

      {/* Card 1: Experience & Tech Stack */}
      <div className="bg-[#2A2A2A] rounded-3xl p-5 text-white  xl:h-[565px]  lg:h-[404px]    xl:w-full lg:w-full md:w-full w-full md:h-[500px]   h-[455px]  flex flex-col justify-between order-5 xl:order-5">
        <div>
          <h3 className="xl:text-[82px] lg:text-[55px] md:text-[82px] text-[82px]  text-white font-bold mb-2">07+</h3>
          <p className="xl:text-[28px] lg:text-[20px] md:text-[28px] text-[28px] text-white mb-4">
            Years Experience<br />
            In this Field.
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
         <Image src={rectanglecategory} alt="Icon" width={24} height={24} className='xl:w-[258px] xl:h-[242.4px] lg:w-[265px] lg:h-[180.4px] md:w-[258px] md:h-[242.4px] w-[258px] h-[242.4px]' />
        </div>
      </div>

    </div>
  )
}
