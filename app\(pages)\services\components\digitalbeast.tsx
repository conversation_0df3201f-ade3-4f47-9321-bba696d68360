"use client"
import Image from "next/image";
import message from "@/public/new assests/news icons/heroicons/services/message.svg";
import music from "@/public/new assests/news icons/heroicons/services/music.svg";
import text from "@/public/new assests/news icons/heroicons/services/text.svg";
import global from "@/public/new assests/news icons/heroicons/services/globl.svg";

const digitalBeastCards = [
  {
    id: 1,
    title: "Full-Stack Web Development",
    description: "From slick landing pages to full-scale SaaS platforms to see us",
    icon: message,
  },
  {
    id: 2,
    title: "UX/UI That Actually Connects",
    description: "Designs that slap — original, bold, and made to convert",
    icon: music,
  },
  {
    id: 3,
    title: "E-Commerce That Sells Like Hell",
    description: "We create e-commerce sites that actually sell, not just exist.",
    icon: text,
  },
  {
    id: 4,
    title: "Ongoing Support & Maintenance",
    description: "Your website will be fast, Google-friendly, and smooth as butter.",
    icon: global,
  }
];

export const DigitalBeast = () => {
  return (
    <div className="w-full bg-black text-white py-16 flex flex-row items-center justify-center">
      <div className="w-[85%] flex flex-row items-center justify-center">
        <div className="w-full grid grid-cols-1 lg:grid-cols-2  items-start">
          {/* Left side - Title */}
        
             <div className="w-[75%] p-3">
            <h2 className="w-full text-4xl md:text-5xl lg:text-6xl  leading-tight">
              We don't just build websites — we build <span className="text-[#FF640F]">digital beasts</span>.
            </h2>
          </div>
        

          {/* Right side - Cards Grid */}
          <div className="w-[100%] grid grid-cols-1 md:grid-cols-2 gap-5 p-2">
            {digitalBeastCards.map((card) => (
              <div
                key={card.id}
                className="bg-[#1B1B1B] cursor-pointer w-[352px] h-[454px] rounded-[50px] border border-[#303030] p-6 hover:bg-[#2c2c2c] flex flex-col justify-between transition-colors duration-300"
              >
                {/* Icon */}
                <div className="w-[82px] h-[82px] bg-white rounded-full flex items-center justify-center mb-6">
                  <Image
                    src={card.icon}
                    alt={card.title}
                    width={24}
                    height={24}
                    className="w-[32px] h-[32px]"
                  />
                </div>

                {/* Content */}
                <div className="flex flex-col  gap-y-6">
                  <h3 className="text-[32px] font-semibold  leading-[32px]">
                    {card.title}
                  </h3>
                  <p className="text-white opacity-45 text-[24px] leading-[32px]">
                    {card.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
     
