import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";

const gradients = [
  "bg-gradient-to-r from-blue-300 to-blue-500",  
  "bg-gradient-to-r from-green-300 to-green-500",  
  "bg-gradient-to-r from-gray-100 to-gray-300",
  "bg-gradient-to-r from-pink-300 to-red-500",  
  "bg-gradient-to-r from-yellow-300 to-orange-500",  
  "bg-gradient-to-r from-teal-300 to-cyan-500",  
];

export const HoverEffect = ({
  items,
  className,
}: {
  items: {
    title: string;
    description: string;
    path: string;
   
  }[];
  className?: string;
}) => {
  let [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  return (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 py-10 gap-6", // Add gap for spacing between cards
        className
      )}
    >
      {items.map((item, idx) => (
        <Link
          href={item.path}
          key={idx}
          className="relative group block p-2 h-full w-full"
          onMouseEnter={() => setHoveredIndex(idx)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <AnimatePresence>
            {hoveredIndex === idx && (
              <motion.span
                className={`absolute inset-0 h-full w-full block rounded-xl ${gradients[idx % gradients.length]}`} // Apply gradient dynamically
                layoutId="hoverBackground"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: 1,
                  transition: { duration: 0.15 },
                }}
                exit={{
                  opacity: 0,
                  transition: { duration: 0.15, delay: 0.2 },
                }}
              />
            )}
          </AnimatePresence>
          <Card>
            <CardTitle>{item.title}</CardTitle>
            <CardDescription>{item.description}</CardDescription>
          </Card>
        </Link>
      ))}
    </div>
  );
};

export const Card = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "rounded-2xl h-full w-full p-10 overflow-hidden bg-[#24252B] border border-transparent dark:border-white/[0.2] group-hover:border-slate-700 relative z-20 transition-all duration-300", // Transition for hover effect
        className
      )}
    >
      <div className="relative z-50">
        <div>{children}</div>
      </div>
    </div>
  );
};

export const CardTitle = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <h4 className={cn("text-zinc-100 font-bold tracking-wide mt-4 text-lg", className)}>
      {children}
    </h4>
  );
};

export const CardDescription = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <p
      className={cn(
        "mt-4 text-zinc-400 tracking-wide leading-relaxed text-sm",
        className
      )}
    >
      {children}
    </p>
  );
};
