'use client';

import React from 'react';

const categories = [
  "All",
  "Subscription",
  "Payment Method",
  "Settings",
  "Account Security",
  "Passwords",
  "Terms & Conditions",
  "Support",
];

const Sidebar = () => {
  return (
    <aside className="mb-10 lg:mb-0 lg:w-1/4">
      {/* Mobile Dropdown */}
      <select className="bg-gray-800 text-white p-3 w-full rounded mb-5 lg:hidden font-medium">
        {categories.map((option) => (
          <option key={option}>{option}</option>
        ))}
      </select>

      {/* Desktop Sidebar */}
      <ul className="hidden lg:block space-y-3 text-gray-400 font-medium">
        {categories.map((item) => (
          <li
            key={item}
            className="cursor-pointer hover:text-white transition duration-200"
          >
            {item}
          </li>
        ))}
      </ul>
    </aside>
  );
};

export default Sidebar;