"use client";

import React, { useState, useEffect } from 'react';
import { 
  FiHelpCircle, 
  FiBriefcase, 
  FiMessageSquare, 
  FiUsers,
  FiTrendingUp,
  FiClock,
  FiCheckCircle,
  FiAlertCircle
} from 'react-icons/fi';
import { getFAQStatistics } from '@/api/faqs/faqs_api';
import { getJobProfileStatistics } from '@/api/jobs/jobs_api';
import { getCommentStatistics } from '@/api/comments/comments_api';
import { getCareerApplicationStatistics } from '@/api/careers/careers_api';

interface StatCard {
  title: string;
  value: string | number;
  icon: React.ElementType;
  color: string;
  bgColor: string;
  change?: string;
  changeType?: 'increase' | 'decrease';
}

interface DashboardStats {
  faqs: {
    total: number;
    active: number;
    inactive: number;
  };
  jobs: {
    total: number;
    active: number;
    inactive: number;
  };
  comments: {
    total: number;
    pending: number;
    approved: number;
  };
  careers: {
    total: number;
    new: number;
    hired: number;
  };
}

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const [faqStats, jobStats, commentStats, careerStats] = await Promise.all([
        getFAQStatistics(),
        getJobProfileStatistics(),
        getCommentStatistics(),
        getCareerApplicationStatistics()
      ]);

      setStats({
        faqs: {
          total: faqStats.data.total,
          active: faqStats.data.active,
          inactive: faqStats.data.inactive
        },
        jobs: {
          total: jobStats.data.total,
          active: jobStats.data.active,
          inactive: jobStats.data.inactive
        },
        comments: {
          total: commentStats.data.total,
          pending: commentStats.data.pending,
          approved: commentStats.data.approved
        },
        careers: {
          total: careerStats.data.total,
          new: careerStats.data.new,
          hired: careerStats.data.hired
        }
      });
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  const getStatCards = (): StatCard[] => {
    if (!stats) return [];

    return [
      {
        title: 'Total FAQs',
        value: stats.faqs.total,
        icon: FiHelpCircle,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
        change: `${stats.faqs.active} active`,
        changeType: 'increase'
      },
      {
        title: 'Job Profiles',
        value: stats.jobs.total,
        icon: FiBriefcase,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        change: `${stats.jobs.active} active`,
        changeType: 'increase'
      },
      {
        title: 'Comments',
        value: stats.comments.total,
        icon: FiMessageSquare,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        change: `${stats.comments.pending} pending`,
        changeType: stats.comments.pending > 0 ? 'decrease' : 'increase'
      },
      {
        title: 'Applications',
        value: stats.careers.total,
        icon: FiUsers,
        color: 'text-purple-600',
        bgColor: 'bg-purple-100',
        change: `${stats.careers.new} new`,
        changeType: 'increase'
      }
    ];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <FiAlertCircle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  const statCards = getStatCards();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome to your admin dashboard. Here's what's happening with your content.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`p-3 rounded-md ${stat.bgColor}`}>
                      <Icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.title}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
                {stat.change && (
                  <div className="mt-4">
                    <div className="flex items-center text-sm">
                      {stat.changeType === 'increase' ? (
                        <FiTrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      ) : (
                        <FiClock className="h-4 w-4 text-yellow-500 mr-1" />
                      )}
                      <span className={`${
                        stat.changeType === 'increase' ? 'text-green-600' : 'text-yellow-600'
                      }`}>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <a
              href="/admin/faqs"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors duration-200"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                  <FiHelpCircle className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Manage FAQs
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  View and manage frequently asked questions
                </p>
              </div>
            </a>

            <a
              href="/admin/jobs"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors duration-200"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                  <FiBriefcase className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Job Profiles
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Manage job postings and applications
                </p>
              </div>
            </a>

            <a
              href="/admin/comments"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors duration-200"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                  <FiMessageSquare className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Comments
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Moderate blog comments and feedback
                </p>
              </div>
            </a>

            <a
              href="/admin/careers"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors duration-200"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                  <FiUsers className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Applications
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Review career applications and CVs
                </p>
              </div>
            </a>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Recent Activity
          </h3>
          <div className="text-sm text-gray-500">
            Activity tracking will be implemented in future updates.
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
