"use client"

import { useState } from 'react';

const AboutIdentity = () => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  const toggleExpand = (index: number) => {
    if (expandedIndex === index) {
      setExpandedIndex(null);
    } else {
      setExpandedIndex(index);
    }
  };

  const dummyData = [
    {
      title: "who we are?",
      content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula."
    },
    {
      title: "what our goal",
      content: "Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor. Ut in nulla enim. Phasellus molestie magna non est bibendum."
    },
    {
      title: "our vision",
      content: "Suspendisse in orci enim. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor."
    }
  ];

  return (
    <div className="flex justify-center items-center w-full pt-[2rem]">
      <div className="flex flex-row justify-between w-full max-w-[90%] gap-20">
        {/* Left Column */}
        <div className='w-[50%]'>
          <h1 className="xl:text-[69px] lg:text-[45px] md:text-[35px] text-[35px] text-white font-bold mb-8 ">Provided Quality Services from 2025</h1>
          
          
        </div>
        
        {/* Right Column */}
        <div className="w-1/2">
          
          <div className="space-y-4">
            {dummyData.map((item, index) => (
              <div 
                key={index} 
                className={`border-b-2 border-white border-opacity-15 w-full overflow-hidden transition-all duration-300 ${expandedIndex === index ? 'pb-4' : ''}`}
              >
                <div 
                  className="flex justify-between items-center py-4 cursor-pointer group"
                  onClick={() => toggleExpand(index)}
                >
                  <h3 className="w-full font-medium text-[28px] leading-[42px] text-white group-hover:text-opacity-80 transition-colors duration-200">
                    {item.title}
                  </h3>
                  <span className="text-2xl text-white transition-transform duration-300">
                    {expandedIndex === index ? '-' : '+'}
                  </span>
                </div>
                {expandedIndex === index && (
                  <div className="text-white text-[22px] leading-[42px] opacity-80 animate-fadeIn pl-1 pr-4">
                    {item.content}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutIdentity;