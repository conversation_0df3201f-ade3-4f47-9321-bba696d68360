'use client';

import React from 'react';
import { Faqs } from '@/data/faq';

const FaqList = () => {
  return (
    <main className="lg:w-3/4 space-y-6">
      <div className="text-sm border border-white w-fit px-3 py-1 rounded-full font-semibold">
        ALL FAQ'S
      </div>
      {Faqs.map((faq, idx) => (
        <details key={idx} className="border-b border-gray-700 py-4 cursor-pointer group">
          <summary className="flex justify-between items-center text-lg font-semibold">
            {faq.question}
            <span className="text-xl group-open:rotate-45 transition-transform">+</span>
          </summary>
          <div className="mt-3 text-gray-300">
            {faq.answer}
          </div>
        </details>
      ))} 
    </main>
  );
};

export default FaqList;