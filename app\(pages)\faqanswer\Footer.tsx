'use client';

import React from 'react';
import { FaFacebook<PERSON>, FaTwitter, FaLinkedinIn } from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="bg-black text-white px-10 py-12">
      {/* Newsletter and Links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 border-b border-gray-800 pb-10">
        {/* Newsletter Subscription */}
        <div>
          <h2 className="text-2xl font-semibold leading-snug">
            Subscribe to our<br />newsletter
          </h2>
          <form className="mt-4">
            <div className="flex">
              <input
                type="email"
                placeholder="Your email address"
                className="px-4 py-2 w-full rounded-l-md bg-white text-black"
              />
              <button type="submit" className="bg-white text-black px-4 py-2 rounded-r-md">
                →
              </button>
            </div>
          </form>
        </div>

        {/* Useful Links */}
        <div className="flex justify-between">
          <div>
            <ul className="space-y-2 text-sm">
              <li className="font-semibold">About</li>
              <li>Work Gallery</li>
              <li>Pricing</li>
              <li>Blog</li>
              <li>Contact</li>
            </ul>
          </div>
          <div>
            <ul className="space-y-2 text-sm">
              <li className="font-semibold">Faq's</li>
              <li>Privacy Policy</li>
              <li>Terms</li>
            </ul>
          </div>
        </div>

        {/* Contact Info */}
        <div className="text-sm">
          <p>82/3 Patel Nagar, Near Metro Station,<br />New Delhi — 110008, India</p>
          <p className="mt-3 font-semibold">(+91) 0187 34 377</p>
          <div className="flex space-x-4 mt-4 text-lg">
            <a href="#"><FaFacebookF /></a>
            <a href="#"><FaTwitter /></a>
            <a href="#"><FaLinkedinIn /></a>
          </div>
        </div>
      </div>

      {/* Bottom Line */}
      <div className="flex flex-col md:flex-row justify-between text-xs text-gray-400 mt-6">
        <p>AGKraft . Partners . Careers</p>
        <p className="text-right mt-2 md:mt-0">@2025 AGKraft. All Right Reserved</p>
      </div>
    </footer>
  );
};

export default Footer;
