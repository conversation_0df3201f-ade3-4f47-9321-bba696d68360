"use client";

import React, { useState, useEffect } from 'react';
import { 
  FiBriefcase, 
  FiMapPin, 
  FiClock, 
  FiDollarSign, 
  FiUsers, 
  FiCalendar,
  FiFilter,
  FiSearch,
  FiRefreshCw
} from 'react-icons/fi';
import { getAllJobProfiles, JobProfile, isJobAcceptingApplications, formatJobDate } from '@/api/jobs/jobs_api';
import CareerApplicationModal from './components/CareerApplicationModal';
import { Toaster } from 'react-hot-toast';

const CareersPage: React.FC = () => {
  const [jobs, setJobs] = useState<JobProfile[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<JobProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedJobType, setSelectedJobType] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<JobProfile | null>(null);
  const [departments, setDepartments] = useState<string[]>([]);

  useEffect(() => {
    fetchJobs();
  }, []);

  useEffect(() => {
    filterJobs();
  }, [jobs, searchQuery, selectedJobType, selectedDepartment]);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await getAllJobProfiles({ status: 'active', limit: 1000 });
      const activeJobs = response.data.filter(job => isJobAcceptingApplications(job));
      setJobs(activeJobs);

      // Extract unique departments
      const uniqueDepartments = [...new Set(activeJobs.map(job => job.department).filter(Boolean))];
      setDepartments(uniqueDepartments);
    } catch (err) {
      console.error('Error fetching jobs:', err);
      setError('Failed to load job openings. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filterJobs = () => {
    let filtered = jobs;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(job => 
        job.jobTitle.toLowerCase().includes(query) ||
        job.jobDescription.toLowerCase().includes(query) ||
        job.techStack.some(tech => tech.toLowerCase().includes(query))
      );
    }

    // Filter by job type
    if (selectedJobType) {
      filtered = filtered.filter(job => job.jobType === selectedJobType);
    }

    // Filter by department
    if (selectedDepartment) {
      filtered = filtered.filter(job => job.department === selectedDepartment);
    }

    setFilteredJobs(filtered);
  };

  const handleApply = (job: JobProfile) => {
    setSelectedJob(job);
    setShowApplicationModal(true);
  };

  const resetFilters = () => {
    setSearchQuery('');
    setSelectedJobType('');
    setSelectedDepartment('');
  };

  const getJobTypeBadge = (jobType: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium";
    switch (jobType) {
      case 'full-time':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'part-time':
        return `${baseClasses} bg-purple-100 text-purple-800`;
      case 'contract':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'internship':
        return `${baseClasses} bg-pink-100 text-pink-800`;
      case 'freelance':
        return `${baseClasses} bg-indigo-100 text-indigo-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0B0A0A] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#0B0A0A] flex items-center justify-center">
        <div className="text-center text-white">
          <FiBriefcase className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Error Loading Jobs</h2>
          <p className="text-gray-400">{error}</p>
          <button 
            onClick={fetchJobs}
            className="mt-4 px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0B0A0A] text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-500 rounded-full mb-6">
            <FiBriefcase className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">
            Join Our Team
          </h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Discover exciting career opportunities and be part of our innovative team. 
            We're always looking for talented individuals to help us build the future.
          </p>
        </div>

        {/* Filters */}
        <div className="bg-gray-900/50 rounded-lg p-6 mb-8 border border-gray-800">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search jobs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Job Type Filter */}
            <select
              value={selectedJobType}
              onChange={(e) => setSelectedJobType(e.target.value)}
              className="px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">All Job Types</option>
              <option value="full-time">Full-time</option>
              <option value="part-time">Part-time</option>
              <option value="contract">Contract</option>
              <option value="internship">Internship</option>
              <option value="freelance">Freelance</option>
            </select>

            {/* Department Filter */}
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">All Departments</option>
              {departments.map((department) => (
                <option key={department} value={department}>
                  {department}
                </option>
              ))}
            </select>

            {/* Reset Button */}
            <button
              onClick={resetFilters}
              className="flex items-center justify-center px-4 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              <FiRefreshCw className="w-4 h-4 mr-2" />
              Reset
            </button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-400">
            {filteredJobs.length} job opening{filteredJobs.length !== 1 ? 's' : ''} available
          </p>
        </div>

        {/* Job Listings */}
        {filteredJobs.length === 0 ? (
          <div className="text-center py-16">
            <FiBriefcase className="mx-auto h-16 w-16 text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Job Openings Found</h3>
            <p className="text-gray-400 mb-6">
              {searchQuery || selectedJobType || selectedDepartment
                ? "No jobs match your current filters. Try adjusting your search criteria."
                : "We don't have any open positions at the moment. Check back soon for new opportunities!"
              }
            </p>
            {(searchQuery || selectedJobType || selectedDepartment) && (
              <button
                onClick={resetFilters}
                className="text-orange-500 hover:text-orange-400 transition-colors"
              >
                Clear all filters
              </button>
            )}
          </div>
        ) : (
          <div className="grid gap-6">
            {filteredJobs.map((job) => (
              <div
                key={job._id}
                className="bg-gray-900/50 rounded-lg p-6 border border-gray-800 hover:border-gray-700 transition-colors"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-white mb-2">
                          {job.jobTitle}
                        </h3>
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400 mb-3">
                          {job.department && (
                            <div className="flex items-center">
                              <FiBriefcase className="w-4 h-4 mr-1" />
                              {job.department}
                            </div>
                          )}
                          {job.location && (
                            <div className="flex items-center">
                              <FiMapPin className="w-4 h-4 mr-1" />
                              {job.location}
                            </div>
                          )}
                          {job.salaryRange && (
                            <div className="flex items-center">
                              <FiDollarSign className="w-4 h-4 mr-1" />
                              {job.salaryRange}
                            </div>
                          )}
                          <div className="flex items-center">
                            <FiCalendar className="w-4 h-4 mr-1" />
                            Apply by {formatJobDate(job.lastDayApplied)}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <span className={getJobTypeBadge(job.jobType)}>
                          {job.jobType}
                        </span>
                        {job.isUrgent && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Urgent
                          </span>
                        )}
                      </div>
                    </div>

                    <p className="text-gray-300 mb-4 line-clamp-3">
                      {job.jobDescription}
                    </p>

                    {/* Tech Stack */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Required Skills:</h4>
                      <div className="flex flex-wrap gap-2">
                        {job.techStack.slice(0, 6).map((tech, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-gray-800 text-gray-300 rounded-full text-sm"
                          >
                            {tech}
                          </span>
                        ))}
                        {job.techStack.length > 6 && (
                          <span className="px-3 py-1 bg-gray-700 text-gray-400 rounded-full text-sm">
                            +{job.techStack.length - 6} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Experience */}
                    {job.experienceRequired && (
                      <div className="mb-4">
                        <span className="text-sm text-gray-400">Experience: </span>
                        <span className="text-sm text-white">{job.experienceRequired}</span>
                      </div>
                    )}
                  </div>

                  <div className="lg:ml-6 mt-4 lg:mt-0">
                    <button
                      onClick={() => handleApply(job)}
                      className="w-full lg:w-auto px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-gray-900"
                    >
                      Apply Now
                    </button>
                    <div className="flex items-center justify-center lg:justify-start mt-2 text-sm text-gray-400">
                      <FiUsers className="w-4 h-4 mr-1" />
                      {job.applicationCount} applicant{job.applicationCount !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Career Application Modal */}
        {showApplicationModal && selectedJob && (
          <CareerApplicationModal
            job={selectedJob}
            isOpen={showApplicationModal}
            onClose={() => {
              setShowApplicationModal(false);
              setSelectedJob(null);
            }}
            onSuccess={() => {
              setShowApplicationModal(false);
              setSelectedJob(null);
              fetchJobs(); // Refresh to update application count
            }}
          />
        )}

        {/* Toast Notifications */}
        <Toaster />
      </div>
    </div>
  );
};

export default CareersPage;
