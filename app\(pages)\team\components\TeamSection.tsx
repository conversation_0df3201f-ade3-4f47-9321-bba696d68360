"use client"

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { FaT<PERSON><PERSON>, FaLinkedinIn, FaInstagram } from 'react-icons/fa'
import { getAllTeamMembers, transformTeamMemberToCard, TeamCardData } from '@/api/team/team_api'

export default function TeamSection() {
  const [teamMembers, setTeamMembers] = useState<TeamCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setLoading(true);
        const teamData = await getAllTeamMembers();
        const transformedTeam = teamData.map(transformTeamMemberToCard);
        setTeamMembers(transformedTeam);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to load team members');
        console.error('Error loading team members:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  if (loading) {
    return (
      <div className="w-full py-16 px-4 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-white">Loading Team Members...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-16 px-4 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-16 px-4 flex flex-col justify-center items-center">
      <div className="w-[90%] text-center flex flex-col justify-center items-center">
        <h2 className="text-[74px] leading-[74px] md:text-5xl font-bold text-white mb-4">
          Meet Our Team
        </h2>
        <p className="w-[68%] text-white opacity-[70%] text-[24px] leading-[37px] mb-12">
          Our founders Gulshan Kumar, Aryan Verma, Kunal Verma met while leading ENGINEERING.
          We try to Build Your Dreams.
        </p>

        {/* Team Grid - 3 columns, 2 rows */}
        <div className="grid grid-cols-3 justify-between gap-8 w-full">
          {teamMembers.map((member) => (
            <div 
              key={member.id} //teamid in postman
              className="relative group cursor-pointer"
              
            >
              {/* Team Member Image */}
              <div className="w-[434.36px] h-[499.51px] rounded-lg overflow-hidden">
                <Image
                  src={member.image} //imageUrl in postman
                  alt={member.name}
                  width={434}
                  height={500}
                  className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                />
                
                {/* Social Media Icons Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-in-out flex items-center justify-center">
                  <div className="flex gap-6 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 ease-out">
                    {member.socialMedia.facebook && (
                      <a 
                        href={member.socialMedia.facebook}  //instagram link in postman
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-[55px] h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaInstagram className="text-[25px]" />
                      </a>
                    )}
                    {member.socialMedia.twitter && (
                      <a 
                        href={member.socialMedia.twitter} //website link in postman
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-[55px] h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaTwitter className="text-[25px]" />
                      </a>
                    )}
                    {member.socialMedia.linkedin && (
                      <a 
                        href={member.socialMedia.linkedin} //linkedin link in postman
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-[55px] h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaLinkedinIn className="text-[25px]" />
                      </a>
                    )}
                  </div>
                </div>
              </div>

              {/* Member Info */}
              <div className="mt-4 text-center">
                {/* name on postman */}
                <h3 className="text-xl font-semibold text-white">{member.name}</h3> 
                {/* jobcategory on postman */}
                <p className="text-white opacity-70 text-sm">{member.role}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
