"use client";

import Image from "next/image";
import { <PERSON>, <PERSON>UpR<PERSON>, MousePointer2 } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import threelines from "@/public/new assests/news icons/heroicons/homeimage/threelines.svg"
import reviewgirl from "@/public/new assests/news icons/heroicons/homeimage/reviewgirl1.png"
import reviewboy from "@/public/new assests/news icons/heroicons/homeimage/reviewboy1.png"

// Review data
const reviews = [
  {
    id: 1,
    name: "<PERSON>",
    image: reviewgirl,
    rating: 5,
    title: "Incredible work!",
    review: "Highly recommend this reliable SaaS provider for seamless workflow optimization.",
    designation: "Senior Officer, Arong"
  },
  {
    id: 2,
    name: "Sumit <PERSON>",
    image: reviewgirl,
    rating: 5,
    title: "Outstanding service!",
    review: "The team delivered beyond our expectations with exceptional quality and professionalism.",
    designation: "Founder, Digital Solutions"
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    image: reviewgirl,
    rating: 5,
    title: "Game changer!",
    review: "This platform has completely transformed how we manage our business operations.",
    designation: "Marketing Director, TechHub"
  },
  {
    id: 4,
    name: "Rahul Verma",
    image: reviewgirl,
    rating: 5,
    title: "Excellent results!",
    review: "Professional team with deep technical expertise. Highly satisfied with the outcome.",
    designation: "Product Manager, InnovateCorp"
  }
];

// Client profile images for header
const clientProfiles = [
  "/assest/review/1.png",
  "/assest/review/2.png",
  "/assest/review/3.png",
  "/assest/review/4.png"
];

export default function HomeReview() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const router = useRouter();

  const handleReviewClick = () => {
    router.push('/allreviewclient.tsx');
  };

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <section className=" py-16 bg-[#0C0C0C]">
      <div className="max-w-full  mx-auto">
        {/* Header Section */}
        <div className="flex flex-row gap-8 mb-16 w-full justify-between px-14">
          {/* Left - Big Number */}
          <div className="lg:col-span-1 w-[50%]">
            <div className="text-left">
              <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold text-white mb-2">
                13,000<span className="text-white">+</span>
              </h1>
              <p className="text-white text-lg md:text-xl">
                Customer love our product
              </p>
            </div>
          </div>

          {/* Right - Client Satisfaction */}
          <div className="lg:col-span-2 flex flex-col justify-center w-[40%]">
            <div className="flex flex-col items-start gap-6 w-full">
              {/* Client Profile Images */}
              <div className="flex flex-row space-x-2 relative">
                {clientProfiles.map((profile, index) => (
                  <div
                    key={index}
                    className="relative w-12 h-12 z-10 rounded-full overflow-hidden border-3 border-white"
                  >
                    <Image
                      src={profile}
                      alt={`Client ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
                <div className="absolute -right-[8rem] -top-10 z-0">
                      <Image
                      src={threelines}
                      alt="threelines"
                      width={24}
                      height={24}
                      className="w-[155px] h-[110px]"/>         
                       </div>
              </div>

              {/* Text with Arrow */}
              <div className="">
                <p className="text-white text-lg md:text-xl font-medium leading-relaxed w-[70%]">
                  Client satisfaction speaks louder than our words. Hear from them.
                </p>
               
              </div>
            </div>
          </div>
        </div>

        {/* Carousel Section */}
        <div className="relative w-full max-w-full mx-auto">
          <Carousel
            setApi={setApi}
            opts={{
              align: "center",
              loop: true,
              containScroll: "trimSnaps",
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-4">
              {reviews.map((review) => (
                <CarouselItem key={review.id} className="pl-4 basis-[70%] md:basis-[70%] lg:basis-[70%]">
                  <div
                    className="bg-[#2B2B2B] backdrop-blur-sm cursor-pointer rounded-3xl p-6 md:p-8 lg:p-10 mx-2 relative group hover:bg-[#333333] transition-all duration-300 hover:scale-[1.02] hover:shadow-xl border border-transparent hover:border-[#19CC35]/20"
                    onClick={handleReviewClick}
                  >
                    {/* Click Indicator - Top Right */}
                    <div className="absolute top-4 right-4 md:top-6 md:right-6 flex items-center justify-center gap-2 w-[3rem] h-[3rem] rounded-full bg-[#737373] text-white/60 group-hover:text-[#FF640F] transition-all duration-300 group-hover:scale-110">
                  
                      <ArrowUpRight className="w-8 h-8 md:w-9 md:h-9 " />
                    </div>

                    {/* Mobile Click Indicator */}
                    <div className="absolute top-4 right-4 md:hidden flex items-center text-white/60 group-hover:text-[#19CC35] transition-all duration-300 group-hover:scale-110">
                      <MousePointer2 className="w-5 h-5 animate-pulse hover:animate-bounce hover:duration-[5000ms] transition-all" />
                    </div>

                    <div className="flex flex-col md:flex-row items-center gap-6 md:gap-8 w-full">
                      {/* Left - Profile Image */}
                      <div className="flex items-center justify-center w-[50%] ">
                        <div className="w-[415px] h-[420px] bg-white  rounded-tl-[350px] rounded-tr-[350px] flex items-center justify-center ">
                          <Image
                            src={review.image}
                            alt={review.name}
                           
                            className=" w-full h-full"
                          />
                        </div>
                      </div>

                      {/* Right - Content */}
                      <div className="flex flex-col justify-between items-start h-full   w-[50%]">
                        <h3 className="text-[52px] md:text-[48px] lg:text-[52px] font-bold underline decoration-[#19CC35] text-white mb-3 md:mb-4  transition-colors duration-300">
                          {review.title}
                        </h3>
                        <p className="text-white  text-base md:text-[36px] lg:text-[36px] leading-[48px] mb-4 md:mb-6">
                          "{review.review}"
                        </p>

                        <div className="flex flex-col w-[90%]  md:flex-row md:items-center justify-between md:justify-between gap-3 md:gap-4">
                          {/* Name and Designation */}
                          <div>
                            <h4 className="text-white  font-bold text-[20px] md:text-[20px]">
                              {review.name},
                            </h4>
                            <p className="text-white opacity-[40%] font-normal text-[20px] md:text-[20px]">
                              {review.designation}
                            </p>
                          </div>

                          {/* Rating */}
                          <div className="flex items-center gap-1 justify-center md:justify-start">
                            {[...Array(review.rating)].map((_, i) => (
                              <Star
                                key={i}
                                className="w-4 h-4 md:w-5 md:h-5 fill-[#f8da33] text-[#f8da33] border-none"
                              />
                            ))}
                            <span className="text-[#ffffff] ml-2 font-semibold text-sm md:text-base">
                              {review.rating}.00
                            </span>
                          </div>
                        </div>

                        {/* View Full Review Button */}
                        {/* <div className="mt-6 w-full">
                          <div className="flex items-center justify-center md:justify-start gap-2 text-white/70 group-hover:text-[#19CC35] transition-colors duration-300">
                            <span className="text-sm font-medium">View Full Review</span>
                            <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                          </div>
                        </div> */}
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>

          {/* Carousel Dots */}
          <div className="flex justify-center gap-2 mt-8">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === current - 1
                    ? "bg-white w-8"
                    : "bg-[#222222] hover:bg-gray-500"
                }`}
                onClick={() => api?.scrollTo(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
      
