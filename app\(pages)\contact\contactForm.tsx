import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { submitContactForm, ContactFormData } from "@/api/contact/contact_api";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    countryCode: "+91",
    phoneNumber: "",
    service: "",
    message: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const contactData: ContactFormData = {
        name: formData.name,
        email: formData.email,
        countryCode: formData.countryCode,
        phoneNumber: formData.phoneNumber,
        service: formData.service,
        message: formData.message
      };

      // Debug: Log the data being sent
      console.log("Form Data being sent:", contactData);
      console.log("Phone Number:", formData.phoneNumber);
      console.log("Country Code:", formData.countryCode);

      const result = await submitContactForm(contactData);

      if (result.success) {
        setSubmitStatus('success');
        // Reset form after successful submission
        setFormData({
          name: "",
          email: "",
          countryCode: "+91",
          phoneNumber: "",
          service: "",
          message: ""
        });
        console.log("Form submitted successfully:", result.message);
      } else {
        setSubmitStatus('error');
        console.error("Form submission failed:", result.message || result.errors);
      }
    } catch (error) {
      setSubmitStatus('error');
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="bg-black text-white py-16 px-4">
      <h2 className="text-center text-[65px] md:text-[72px] font-semibold mb-10">
        Questions? Feel Free to Reach<br></br> Out Via Message.
      </h2>

      <form
        onSubmit={handleSubmit}
        className="max-w-[80%] mx-auto bg-zinc-900 rounded-xl p-10 space-y-6 w-[1800px]"
      >
        {/* Name Input */}
          
          
        <div className="flex flex-col md:flex-col gap-20">
          <div className="flex flex-row w-full gap-10 ">
          <div className="w-1/2">
            <label className="block mb-1 text-[20px]">Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full bg-transparent border-b border-zinc-700 outline-none py-2 text-white placeholder-gray-400 autofill:bg-transparent autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_transparent]"
              style={{
                WebkitBoxShadow: "inset 0 0 0 1000px transparent",
                WebkitTextFillColor: "white",
                transition: "background-color 5000s ease-in-out 0s"
              }}
              placeholder="Your Name"
              required
            />
          </div>

          {/* Email Input */}
          <div className="w-1/2">
            <label className="block mb-1 text-[20px]">Email*</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full bg-transparent border-b border-zinc-700 outline-none py-2 text-white placeholder-gray-400  autofill:text-white "
             
              placeholder="<EMAIL>"
              required
            />
          </div>
        </div>

        <div className="flex flex-row w-full gap-10">
          {/* Phone Number with Country Code */}
          <div className="w-1/2">
            <label className="block mb-1 text-[20px]">Phone Number</label>
            <div className="flex gap-2">
              {/* Country Code Dropdown */}
              <div className="w-32">
                <Select value={formData.countryCode} onValueChange={(value) => handleSelectChange("countryCode", value)}>
                  <SelectTrigger className="bg-transparent border-b border-zinc-700 border-t-0 border-l-0 border-r-0 rounded-none text-white focus:ring-0 focus:ring-offset-0 focus:border-zinc-700">
                    <SelectValue placeholder="Code" />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-800 border-zinc-700 text-white">
                    <SelectItem value="+1">🇺🇸 +1</SelectItem>
                    <SelectItem value="+91">🇮🇳 +91</SelectItem>
                    <SelectItem value="+44">🇬🇧 +44</SelectItem>
                    <SelectItem value="+86">🇨🇳 +86</SelectItem>
                    <SelectItem value="+81">🇯🇵 +81</SelectItem>
                    <SelectItem value="+49">🇩🇪 +49</SelectItem>
                    <SelectItem value="+33">🇫🇷 +33</SelectItem>
                    <SelectItem value="+39">🇮🇹 +39</SelectItem>
                    <SelectItem value="+34">🇪🇸 +34</SelectItem>
                    <SelectItem value="+7">🇷🇺 +7</SelectItem>
                    <SelectItem value="+55">🇧🇷 +55</SelectItem>
                    <SelectItem value="+61">🇦🇺 +61</SelectItem>
                    <SelectItem value="+82">🇰🇷 +82</SelectItem>
                    <SelectItem value="+52">🇲🇽 +52</SelectItem>
                    <SelectItem value="+31">🇳🇱 +31</SelectItem>
                    <SelectItem value="+46">🇸🇪 +46</SelectItem>
                    <SelectItem value="+47">🇳🇴 +47</SelectItem>
                    <SelectItem value="+45">🇩🇰 +45</SelectItem>
                    <SelectItem value="+41">🇨🇭 +41</SelectItem>
                    <SelectItem value="+43">🇦🇹 +43</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* Phone Number Input */}
              <div className="flex-1">
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  className="w-full bg-transparent border-b border-zinc-700 outline-none py-2 text-white placeholder-gray-400 autofill:bg-transparent autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_transparent]"
                  style={{
                    WebkitBoxShadow: "inset 0 0 0 1000px transparent",
                    WebkitTextFillColor: "white",
                    transition: "background-color 5000s ease-in-out 0s"
                  }}
                  placeholder="Your phone number"
                />
              </div>
            </div>
          </div>

          {/* Service Input */}
          <div className="flex flex-col w-1/2">
            <label className="block mb-1 text-sm">Service</label>
            <input
              type="text"
              name="service"
              value={formData.service}
              onChange={handleChange}
              className="w-full bg-transparent border-b border-zinc-700 outline-none py-2 text-white placeholder-gray-400"
              placeholder="What service are you interested in?"
            />
          </div>
        </div>

        {/* Message Box */}
        <div>
          <label className="block mb-1 text-sm">Your Message*</label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            rows={4}
            className="w-full bg-transparent border-b border-zinc-700 outline-none py-2 text-white placeholder-gray-400 resize-none"
            placeholder="Write your message here..."
            required
          />
        </div>

        {/* Status Messages */}
        {submitStatus === 'success' && (
          <div className="text-green-400 text-center py-2">
            Message sent successfully! We'll get back to you soon.
          </div>
        )}
        {submitStatus === 'error' && (
          <div className="text-red-400 text-center py-2">
            Failed to send message. Please try again.
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-400 disabled:cursor-not-allowed transition-colors text-white py-2 rounded-md font-medium"
        >
          {isSubmitting ? "SENDING..." : "SEND MESSAGE"}
        </button>
        </div>
      </form>
    </section>
  );
};

export default ContactForm;
