import type { <PERSON>ada<PERSON> } from "next";
import { Poppin<PERSON> } from "next/font/google";
import "./globals.css";
import Script from "next/script";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
  fallback: ["sans-serif"], // Fallback font
});

export const metadata: Metadata = {
  title:
    "AGKRAFT - Software Development | Web Development | Mobile App Development",
  description:
    "AGKRAFT - Your trusted technology partner for professional software development, web design, web application development, mobile app development (Android/iOS), eCommerce solutions, and cutting-edge technology services. Empowering businesses with custom and scalable solutions.",
  keywords: [
    "software development company",
    "web development services",
    "mobile app development",
    "eCommerce development",
    "custom software solutions",
    "Android app development",
    "iOS app development",
    "business technology solutions",
    "responsive web design",
    "AGKRAFT services",
    "tech solutions",
    "web application development",
    "best software company",
    "website design company",
    "top mobile app developers",
    "custom web solutions",
    "technology partner",
  ],
  authors: [{ name: "Kunal Verma" }, { name: "<PERSON><PERSON><PERSON>" }, { name: "Aryan" }],
  robots: "index, follow",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={poppins.variable}>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta
          property="og:title"
          content={metadata.title?.toString() || undefined}
        />
        <meta
          property="og:description"
          content={metadata.description || undefined}
        />
        <meta property="og:image" content="/path/to/preview-image.jpg" />
        <meta property="og:url" content="https://agkraft.in" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content={metadata.title?.toString() || undefined}
        />
        <meta name="twitter:description" content={metadata.description || ""} />
        <meta name="twitter:image" content="/path/to/preview-image.jpg" />
        <link rel="canonical" href="https://agkraft.in/" />
      </head>

      <body className="bg-[#0B0A0A]">
        {/* ✅ Google Tag Manager (noscript) - BODY */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-PL9XL4JJ"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe>
        </noscript>

        {children}

        {/* ✅ GTM Script */}
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-PL9XL4JJ');
            `,
          }}
        />

        {/* ✅ Google Ads */}
        <Script
          async
          src="https://www.googletagmanager.com/gtag/js?id=AW-16870433416"
        />
        <Script
          id="google-ads"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'AW-16870433416');
            `,
          }}
        />

        {/* ✅ Google Ads Conversion Tracking */}
        <Script
          id="google-conversion"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              gtag('event', 'conversion', {
                'send_to': 'AW-16870433416/tpjqCIXD_KAaEIjFuew-'
              });
            `,
          }}
        />

        {/* ✅ JSON-LD Structured Data */}
        <Script
          id="structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "AGKRAFT",
              url: "https://agkraft.in/",
              logo: "/path/to/logo.png",
              description:
                "AGKRAFT specializes in web and mobile app development, offering services in software development, eCommerce solutions, and business technology support.",
              sameAs: [
                "https://www.linkedin.com/company/agkraft",
                "https://www.instagram.com/agkraft",
                "https://www.facebook.com/agkraft",
                "https://twitter.com/agkraft",
              ],
              contactPoint: [
                {
                  "@type": "ContactPoint",
                  telephone: "+91-8383049814, 9911572491, 9262975957",
                  contactType: "Customer Service",
                },
              ],
            }),
          }}
        />
      </body>
    </html>
  );
}
