"use client";

import { Marquee } from "@/components/magicui/marquee";
import { FaStar } from "react-icons/fa";

const reviews = [
  {
    name: "<PERSON>",
    rating: 5,
    text: "Faucibus et, elit ipsum eu pulvir neque leo",
    highlightText: "eros pulvinar",
    endText: "nec facilisis.",
    color: "text-green-400",
    highlightColor: "text-green-400",
    height: "h-48",
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "User-friendly,",
    highlightText: "efficient — the best",
    endText: "SaaS experience!",
    color: "text-pink-400",
    highlightColor: "text-pink-400",
    height: "h-40",
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "Incredible SaaS innovation — simplified tasks, increased efficiency.",
    highlightText: "A game-changer",
    endText: "for our business success.",
    color: "text-yellow-400",
    highlightColor: "text-yellow-400",
    height: "h-56",
  },
  {
    name: "<PERSON><PERSON>",
    rating: 5,
    text: "Faucibus et, elit ipsum eu pulvir neque leo",
    highlightText: "eros pulvinar",
    endText: "nec facilisis massa.",
    color: "text-blue-400",
    highlightColor: "text-blue-400",
    height: "h-52",
  },
  {
    name: "Jon Cooper",
    rating: 5,
    text: "Faucibus et, elit ipsum eu pulvir neque leo",
    highlightText: "eros pulvinar",
    endText: "nec facilisis.",
    color: "text-blue-700",
    highlightColor: "text-blue-700",
    height: "h-44",
  },
  {
    name: "Daniel Thomas",
    rating: 5,
    text: "Incredible",
    highlightText: "SaaS",
    endText: "",
    color: "text-orange-400",
    highlightColor: "text-orange-400",
    height: "h-36",
  },
  {
    name: "Lassy Chester",
    rating: 5,
    text: "User-friendly,",
    highlightText: "efficient — the best",
    endText: "SaaS experience!",
    color: "text-purple-600",
    highlightColor: "text-purple-600",
    height: "h-48",
  },
];

export default function ReviewMarqueeHorizontal() {
  // Split reviews into columns for masonry layout exactly like image
  const column1 = [
    { ...reviews[0], height: "h-48" }, // Jon Cooper - green
    { ...reviews[3], height: "h-56" }, // Daniel Thomas - yellow (longer)
  ];

  const column2 = [
    { ...reviews[1], height: "h-40" }, // Maria Gomez - purple
    { ...reviews[4], height: "h-52" }, // Lassy Chester - blue
    { ...reviews[5], height: "h-44" }, // Another review
  ];

  const column3 = [
    { ...reviews[2], height: "h-60" }, // Daniel Thomas - yellow (longest)
    { ...reviews[6], height: "h-36" }, // Short review
    { ...reviews[0], height: "h-48" }, // Jon Cooper - green
  ];

  return (
    <div className="w-full  flex flex-col items-center justify-center mt-20 ">
        <div className="bg-[#0C0C0C] w-[80%] py-16 px-4 overflow-hidden rounded-[50px] flex flex-col items-center justify-center">
      <div className="text-center mb-12">
        <h2 className="text-[65px] md:text-[72px] font-bold leading-[90px] tracking-[-1px] text-white mb-4">
          Join 3 million writers
        </h2>
        <p className="text-white text-[24px]">
          AGKRafi is rating 4.9/5 with over 3000+ reviews online.
        </p>
      </div>

      {/* Container with fixed height and shadows */}
      <div className="relative max-w-7xl mx-auto h-[600px] overflow-hidden">
        {/* Top shadow gradient */}
        <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-[#0C0C0C] to-transparent z-10 pointer-events-none"></div>

        {/* Bottom shadow gradient */}
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-[#0C0C0C] to-transparent z-10 pointer-events-none"></div>

        {/* Animated Masonry columns */}
        <div className="flex gap-6 justify-center h-full">
          {/* Column 1 - Vertical Marquee */}
          <div className="flex-1 max-w-sm">
            <Marquee className="[--duration:40s] h-full" vertical pauseOnHover>
              {column1.concat(column1).map((review, index) => (
                <div
                  key={index}
                  className={` p-6 rounded-xl w-full ${review.height} flex flex-col justify-between text-white mb-6 cursor-pointer border border-[#282828] bg-[#242424]`}
                >
                  <div>
                    <div className="flex gap-1 mb-4">
                      {Array.from({ length: 5 }, (_, i) => (
                        <FaStar
                          key={i}
                          className={`text-sm ${i < review.rating ? review.color : "text-gray-600"}`}
                        />
                      ))}
                    </div>
                    <p className="text-sm leading-relaxed text-gray-300">
                      "{review.text} <span className={review.highlightColor}>{review.highlightText}</span> {review.endText}"
                    </p>
                  </div>
                  <p className="text-right text-sm mt-4 text-gray-400 font-medium">
                    {review.name}
                  </p>
                </div>
              ))}
            </Marquee>
          </div>

          {/* Column 2 - Reverse Vertical Marquee */}
          <div className="flex-1 max-w-sm">
            <Marquee className="[--duration:50s] h-full" vertical reverse pauseOnHover>
              {column2.concat(column2).map((review, index) => (
                <div
                  key={index}
                  className={` p-6 rounded-xl w-full ${review.height} flex flex-col justify-between text-white mb-6 cursor-pointer border border-[#282828] bg-[#242424]`}
                >
                  <div>
                    <div className="flex gap-1 mb-4">
                      {Array.from({ length: 5 }, (_, i) => (
                        <FaStar
                          key={i}
                          className={`text-sm ${i < review.rating ? review.color : "text-gray-600"}`}
                        />
                      ))}
                    </div>
                    <p className="text-sm leading-relaxed text-gray-300">
                      "{review.text} <span className={review.highlightColor}>{review.highlightText}</span> {review.endText}"
                    </p>
                  </div>
                  <p className="text-right text-sm mt-4 text-gray-400 font-medium">
                    {review.name}
                  </p>
                </div>
              ))}
            </Marquee>
          </div>

          {/* Column 3 - Vertical Marquee */}
          <div className="flex-1 max-w-sm">
            <Marquee className="[--duration:45s] h-full" vertical pauseOnHover>
              {column3.concat(column3).map((review, index) => (
                <div
                  key={index}
                  className={` p-6 rounded-xl w-full ${review.height} flex flex-col justify-between text-white mb-6 cursor-pointer border border-[#282828] bg-[#242424]`}
                >
                  <div>
                    <div className="flex gap-1 mb-4">
                      {Array.from({ length: 5 }, (_, i) => (
                        <FaStar
                          key={i}
                          className={`text-sm ${i < review.rating ? review.color : "text-gray-600"}`}
                        />
                      ))}
                    </div>
                    <p className="text-sm leading-relaxed text-gray-300">
                      "{review.text} <span className={review.highlightColor}>{review.highlightText}</span> {review.endText}"
                    </p>
                  </div>
                  <p className="text-right text-sm mt-4 text-gray-400 font-medium">
                    {review.name}
                  </p>
                </div>
              ))}
            </Marquee>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
}