// Mobile Application Interface Project Details
import img6 from "@/public/new assests/proejct/idcsite.png"

export const mobileApplicationInterfaceDetails = {
  id: "03",
  uniqueId: "mobile-application-interface",
  title: "Mobile Application Interface Design",
  category: "Mobile Development",
  overview: {
    description: "Created a modern mobile application interface that combines cutting-edge design trends with exceptional usability. The project focused on delivering a seamless user experience across iOS and Android platforms while maintaining brand consistency.",
    description2: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullaum laboris nisi ut aliquip ex ea commodo consequat.",
    challenge: "Designing for multiple platforms while ensuring consistent user experience and optimal performance across different device sizes and operating systems.",
    solution: "We developed a comprehensive design system with platform-specific adaptations, ensuring native feel while maintaining design consistency and brand identity."
  },
  projectDetails: {
    client: "Digital Agency",
    duration: "5 Months", 
    categories: "ui/ux Design & Development",
    website: "www.google.com",
  },
  processSteps: [
   
    {
      step: "02",
      title: "Process & Challenge",
      description: "Our design process involved multiple phases of iteration and testing:",
      description2: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.",
      points: [
        "Creating platform-specific design guidelines",
        "Implementing gesture-based navigation patterns",
        "Optimizing for one-handed usage scenarios",
        "Ensuring accessibility across different user abilities"
      ]
    },
    {
      step: "03",
      title: "Summary",
      description: "The final mobile application achieved a 4.8-star rating on app stores and significantly improved user engagement metrics. The design system we created enables rapid prototyping and consistent feature development."
    }
  ],
  images: {
    hero: img6,
    gallery: [img6, img6, img6]
  },
  technologies: ["React Native", "Swift", "Kotlin", "Firebase"],
  features: [
    "Cross-platform Compatibility",
    "Offline Functionality",
    "Push Notifications",
    "Biometric Authentication",
    "Social Integration"
  ],
  nextProject: {
    id: "04",
    title: "Website User Interface",
    slug: "website-user-interface"
  },
  previousProject: {
    id: "02",
    title: "SAAS User Interface",
    slug: "saas-user-interface"
  }
};
