"use client";
import logo from "@/public/assest/logo.png"
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 100); // Become sticky after 100px scroll
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  

  return (
    <nav className={`w-full flex flex-row rounded-[100px] transition-all duration-500 ease-in-out ${
      isScrolled
        ? 'fixed top-0 left-0 right-0 z-[150] transform translate-y-0 '
        : 'relative transform translate-y-0'
    }`}>
     <div className={`transition-all duration-500 ease-in-out w-full flex flex-row justify-between rounded-[100px] ${
       isScrolled
         ? 'mx-2 my-2 bg-[#1D1B1B] shadow-lg border border-white/10'
         : 'm-4 bg-[#1D1B1B]'
     }`}>
       {/* logo image */}
      <div className={`transition-all duration-500 ease-in-out ${
        isScrolled ? 'p-2 pl-8' : 'p-2 pl-10'
      }`}>
        <Image
          src={logo}
          alt="logo"
          className={`transition-all duration-500 ease-in-out ${
            isScrolled ? 'w-[50px] h-[56px]' : 'w-[58px] h-[64px]'
          }`}
        />
      </div>
      {/* tabs */}
     {/* Hamburger Icon for Mobile */}
        <div className={`sm:block md:hidden transition-all duration-500 ease-in-out ${
          isScrolled ? 'pr-3' : 'pr-4'
        }`}>
          <button onClick={toggleMenu} className="text-white focus:outline-none">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>

        {/* Navigation Links */}
        <div
          className={`${
            isMenuOpen ? "flex" : "hidden"
          } sm:flex-col  md:flex md:flex-row md:items-center transition-all duration-500 ease-in-out ${
            isScrolled ? 'md:space-x-12 ' : 'md:space-x-14 '
          }`}
        >
          <Link href="/" className="text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center">
            Home
          </Link>
          <Link href="/about" className="text-white text-[22px] leading-[46px]   hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center">
            About Us
          </Link>
          <Link href="/services" className="text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center">
            Services
          </Link>
          <Link href="/contact" className="text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out sm:text-center">
            Contact
          </Link>
          <Link href="/blog" className="text-white text-[22px] leading-[46px] hover:text-[#FF640F] transition-all duration-300 ease-in-out  sm:text-center">
            Blog
          </Link>
     </div>
      {/* singups */}
      <div>
        
      </div>
     </div>

    </nav>
  );
};

export default Navbar;