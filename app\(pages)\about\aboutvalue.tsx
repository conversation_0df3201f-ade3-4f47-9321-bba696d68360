"use client";
import commandclientaboutus from "@/public/new assests/news icons/heroicons/homeimage/commandclientaboutus.png";
import { FaPlus, FaMinus } from "react-icons/fa";
import { useState } from "react";

// Define the accordion data as an array of objects
const accordionData = [
  {
    title: "Our mission for business",
    content:
      "Our founders, <PERSON> and <PERSON> met while leading Engineering. tortor tortor porttitor vitae commodo et.",
  },
  {
    title: "What's our goal",
    content:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    title: "Our vision",
    content:
      "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
  },
];

export default function AboutValue() {
  const [openAccordion, setOpenAccordion] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenAccordion(openAccordion === index ? null : index);
  };

  return (
    <div className="w-full text-white flex justify-center items-center">
      <div className="flex flex-row w-[90%] gap-10 items-center justify-between ">
        {/* Left Section with Image */}
        <div className="flex-1 relative">
          <img
            src={commandclientaboutus.src}
            alt="Team working"
            className="w-[659px] h-[663px]"
          />
          <div className="absolute bottom-5 right-[5rem] bg-white text-black p-4 text-center">
            <h3 className="text-[64px] ">07+</h3>
            <span className="text-[20px] mt-1">Years experience <br/> with proud.</span>
          </div>
        </div>

        {/* Right Section with Tagline, Headline, and Accordion */}
        <div className="flex flex-col justify-between w-1/2 gap-20">
         <div>
             <div className="uppercase text-[20px] leading-[100%] tracking-[3px] text-white opacity-60 mb-2">
            Our Values
          </div>
          <div className="text-[72px] font-bold leading-[74px] tracking-[-2px] mb-5">
            Helping businesses  grow with
            <span className="text-white">expertise.</span>
          </div>
         </div>
          <div className="mt-5">
            {accordionData.map((item, index) => (
              <div key={index} className="border-b border-gray-700">
                <div
                  className="flex justify-between items-center py-4 cursor-pointer"
                  onClick={() => toggleAccordion(index)}
                >
                  <span className="text-[28px] leading-[100%] tracking-[-1px]">{item.title}</span>
                  {openAccordion === index ? (
                    <FaMinus className="text-[28px] text-white" />
                  ) : (
                    <FaPlus className="h-6 w-6 text-white" />
                  )}
                </div>
                <div
                  className={`text-sm text-gray-400 transition-all duration-500 ease-in-out overflow-hidden ${
                    openAccordion === index
                      ? "max-h-40 opacity-100 "
                      : "max-h-0 opacity-0 pb-10"
                  }`}
                >
                  <div className="py-8 text-[22px] leading-[42px] tracking-[-1px]">{item.content}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}