"use client";
import {
  useM<PERSON>V<PERSON><PERSON><PERSON><PERSON>,
  useScroll,
  useTransform,
  motion,
} from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import PulsatingButton from "@/components/ui/pulsating-button";
import Link from "next/link";


interface TimelineEntry {
  title: string;
  content: React.ReactNode;
}

export const Timeline = ({ data }: { data: TimelineEntry[] }) => {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setHeight(rect.height);
    }
  }, [ref]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0%", "end 20%"],
  });

  const heightTransform = useTransform(scrollYProgress, [0, 1], [0, height]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1], [0, 1]);
// #f06622
  return (
    <div className="w-full font-sans md:px-0" ref={containerRef}>
      <div className=" mx-auto px-0 md:px-0 lg:px-6 mt-0 lg:mt-16">
        <div className="flex flex-col md:flex-row items-center gap-2 sm:gap-3">
          <span className="text-white whitespace-pre-wrap text-left relative z-20 mt-2 bg-clip-text text-lg md:text-xl lg:text-2xl xl:text-4xl mb-4 leading-none tracking-wide text-transparent">
            Comprehensive
          </span>
          <span className="whitespace-pre-wrap text-left relative z-20 mt-2 bg-clip-text text-lg md:text-xl lg:text-2xl xl:text-4xl mb-4 leading-none tracking-wide text-[#f06622]">
            Software/Marketing Services
          </span>
          <span className="text-white whitespace-pre-wrap text-left relative z-20 mt-2 bg-clip-text text-lg md:text-xl lg:text-2xl xl:text-4xl mb-4 leading-none tracking-wide text-transparent">
            Tailored to Your Needs
          </span>
        </div>
        <div className="flex flex-row justify-between items-center gap-x-2">
          <div>
            <p className="text-neutral-300 text-sm md:text-base max-w-[42rem] xl:max-w-5xl">
            AGKraft helps your business grow with services like web and app development, SEO, iOS development, and digital marketing. We create solutions that boost your online presence and help you reach more customers. Let’s grow together!
            </p>
          </div>
          <div className="hidden lg:block">
          <Link href={"/contact"} ><PulsatingButton>Consult Experts</PulsatingButton>;</Link>
          </div>
        </div>
      </div>
      <div ref={ref} className="relative max-w-[1440px] mx-auto ">
        {data.map((item, index) => (
          <div
            key={index}
            className="flex justify-start pt-8 md:pt-14 md:gap-10"
          >
            <div className="sticky flex flex-col md:flex-row z-40 items-center top-40 self-start max-w-xs lg:max-w-lg md:w-full">
              <div className="h-10 absolute left-3 md:left-3 w-10 rounded-full bg-[#5592de] flex items-center justify-center">
                <div className="h-4 w-4 rounded-full bg-neutral-200 dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 p-2" />
              </div>
              <h3 className="hidden md:block text-xl md:pl-20 md:text-2xl font-semibold whitespace-pre-wrap text-left relative z-20 bg-clip-text text-[#ffffff] ">
                {item.title}
              </h3>
            </div>

            <div className="relative pl-20 pr-4 md:pl-4 w-full">
              <h3 className="md:hidden block text-xl mb-4 text-left font-bold  dark:text-gray-500 whitespace-pre-wrap relative z-20 bg-clip-text text-[#f06622]">
                {item.title}
              </h3>
              {item.content}{" "}
            </div>
          </div>
        ))}
        <div
          style={{
            height: height + "px",
          }}
          className="absolute md:left-8 left-8 top-0 overflow-hidden w-[2px] bg-[linear-gradient(to_bottom,var(--tw-gradient-stops))] from-transparent from-[0%] via-neutral-200 dark:via-neutral-700 to-transparent to-[99%]  [mask-image:linear-gradient(to_bottom,transparent_0%,black_10%,black_90%,transparent_100%)] "
        >
          <motion.div
            style={{
              height: heightTransform,
              opacity: opacityTransform,
            }}
            className="absolute inset-x-0 top-0  w-[2px] bg-gradient-to-t from-purple-500 via-blue-500 to-transparent from-[0%] via-[10%] rounded-full"
          />
        </div>
      </div>
    </div>
  );
};
