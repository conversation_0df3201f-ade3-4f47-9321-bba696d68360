"use client"
import React from 'react'
import Image from 'next/image'
import { RiFacebookFill } from "react-icons/ri";
import { FaXTwitter } from "react-icons/fa6";
import { IoLogoInstagram } from "react-icons/io5";

interface ProjectHeroProps {
  projectDetails: any
}

export default function ProjectHero({ projectDetails }: ProjectHeroProps) {
  return (
    <div className="w-full min-h-screen  text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px]">
      <div className="max-w-[95%] mx-auto px-10">
        {/* title */}
        <div className='flex flex-row border-b border-white/10 justify-between items-center'>
          <div className='w-[70%]'>
            <h1 className="text-[48px] md:text-[60px] lg:text-[72px] font-bold leading-tight tracking-[-2px] mb-8">
              {projectDetails.title}
              {/* title in postman */}
            </h1>
          </div>
          <div className='w-[70%] flex flex-row justify-end items-end gap-4'>
            <div className='w-[48px] h-[48px] border flex items-center cursor-pointer justify-center hover:bg-white hover:text-black text-white border-white/50 rounded-full '>
              <RiFacebookFill className=' rounded-full text-[24px]' />
            </div>
            <div className='w-[48px] h-[48px] border flex items-center cursor-pointer  justify-center hover:bg-white hover:text-black border-white/50 rounded-full '>
              <FaXTwitter className='rounded-full text-[24px]' />
            </div>
            <div className='w-[48px] h-[48px] border flex items-center cursor-pointer justify-center hover:bg-white hover:text-black border-white/50 rounded-full '>
              <IoLogoInstagram className=' rounded-full text-[24px]' />
            </div>


          </div>

        </div>





        {/* Project Header */}
        <div className="mb-[48px] md:mb-[64px] lg:mb-[80px] mt-10">


          <div className="text-[48px] md:text-[45px] lg:text-[45px] font-bold leading-[70px] tracking-[-1px]  flex flex-row items-start justify-start gap-8">
            <span className="w-[10%] text-[48px] md:text-[48px] lg:text-[48px] font-bold text-white">
              01
            </span>
            <div className='w-[90%] flex flex-col justify-between gap-6 items-start'>
              <span className=' text-[48px] md:text-[45px] lg:text-[45px] font-bold leading-[70px] tracking-[-1px]'> Project Overview</span>

             
                <div>
                  <div
                    className="text-[18px] md:text-[20px] lg:text-[22px] text-white/70  leading-[40px] mb-8 prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: projectDetails.overview.description }}
                  />
                

                <div className=" flex flex-row justify-between mt-6">
                  <div className='flex flex-col items-start justify-center gap-y-4'>
                    <span className="text-[16px] font-semibold text-white leading-none">CLIENT</span>
                    <span className="text-[20px] text-white/50 leading-[30px]">{projectDetails.projectDetails.client}</span>
                    {/* clientName in postman */}
                  </div>

                  <div className='flex flex-col items-start justify-center gap-y-4'>
                    <span className="text-[16px] font-semibold text-white/80 leading-none">DURATION</span>
                    <span className="text-[20px] text-white/50 leading-[30px]">{projectDetails.projectDetails.duration}</span>
                    {/* projectDeliveryDate in postman */}
                  </div>

                  <div className='flex flex-col items-start justify-center gap-y-4'>
                    <span className="text-[16px] font-semibold text-white/80 leading-none">CATEGORY</span>
                    <span className="text-[20px] text-white/50 leading-[30px]">{projectDetails.projectDetails.categories}</span>
                    {/* projectCategory in postman */}
                  </div>

                  <div className='flex flex-col items-start justify-center gap-y-4'>
                    <span className="text-[16px] font-semibold text-white/80 leading-none">WEBSITES</span>
                    <span className="text-[20px] text-white/50 leading-[30px]">{projectDetails.projectDetails.website}</span>
                    {/* projectLink in postman */}
                  </div>
                </div>
              </div>
              {/* Hero Image */}
              <div className=" w-full h-[400px] md:h-[500px] lg:h-[640px] rounded-[24px] overflow-hidden bg-[#1a1a1a]">
                <Image
                  src={projectDetails.images.hero}
                  alt={projectDetails.title}
                  width={1200}
                  height={640}
                  className="object-cover w-full h-full"
                />
              </div>
            </div>
          </div>


        </div>


      </div>
    </div>
  )
}
