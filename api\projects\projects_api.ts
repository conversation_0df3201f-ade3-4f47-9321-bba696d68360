import axios from "axios";

// Base URL configuration
const BASE_URL = "http://localhost:8001/api";

// Create axios instance with base configuration
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    }
});

// Project interface based on API response
export interface Project {
    _id: string;
    title: string;
    description: string;
    bigImageUrl: string;
    processAndChallengeDescription: string;
    processAndChallengePoints: string[];
    processAndChallengeDescription2: string;
    miniImages: string[];
    summaryDescription: string;
    projectCategory: string;
    status: string;
    featured: boolean;
    views: number;
    tags: string[];
    projectLink: string;
    createdAt: string;
    updatedAt: string;
    id: number;
    __v: number;
    clientName?: string;
    projectDeliveryDate?: string;
}

// API Response interface for get all projects
export interface ProjectsResponse {
    data: Project[];
}

// API Response interface for get project by ID
export interface ProjectByIdResponse {
    status: boolean;
    code: number;
    message: string;
    data: Project;
}

// Get all projects
export const getAllProjects = async (): Promise<Project[]> => {
    try {
        const response = await apiClient.get<ProjectsResponse>('/projects');
        return response.data.data;
    } catch (error: any) {
        console.error('Error fetching projects:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch projects');
    }
};

// Get project by ID
export const getProjectById = async (id: string | number): Promise<Project> => {
    try {
        const response = await apiClient.get<ProjectByIdResponse>(`/projects/${id}`);
        return response.data.data;
    } catch (error: any) {
        console.error('Error fetching project by ID:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch project');
    }
};

// Utility function to truncate project description to specified word limit
export const truncateDescription = (description: string, wordLimit: number = 10): string => {
    const words = description.split(' ');
    if (words.length <= wordLimit) {
        return description;
    }
    return words.slice(0, wordLimit).join(' ') + '...';
};

// Utility function to generate project URL slug
export const generateProjectUrl = (id: string, title: string): string => {
    const slug = title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .trim();
    
    return `/project-details/${id}/${slug}`;
};

// Transform API project data for project cards
export interface ProjectCardData {
    id: string;
    serialNo: number;
    category: string;
    title: string;
    description: string;
    bigImageUrl: string;
    slug: string;
}

export const transformProjectToCard = (project: Project, index: number): ProjectCardData => {
    return {
        id: project._id,
        serialNo: index + 1, // Serial number starting from 1
        category: project.projectCategory,
        title: project.title,
        description: truncateDescription(project.description, 15), // Truncate to 15 words for cards
        bigImageUrl: project.bigImageUrl,
        slug: project.title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim()
    };
};
