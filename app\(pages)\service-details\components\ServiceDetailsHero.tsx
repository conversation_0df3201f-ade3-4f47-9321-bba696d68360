'use client';

import { useState } from 'react';
import { Service } from '@/api/services/services_api';
import { FaCheck } from "react-icons/fa6";

interface ServiceDetailsHeroProps {
  service: Service;
}

// Video mapping based on service - uses service video or default
const getVideoForService = (service: Service): string => {
  // Use service-specific video if available, otherwise use default
  return service.videoUrl || "/new assests/news icons/heroicons/services/newreduces.mp4";
};

export default function ServiceDetailsHero({ service }: ServiceDetailsHeroProps) {
  const [openAccordion, setOpenAccordion] = useState<number | null>(0); // First item open by default
  const videoSrc = getVideoForService(service);

  const toggleAccordion = (index: number) => {
    setOpenAccordion(openAccordion === index ? null : index);
  };

  return (
    <div className="space-y-12">
      {/* Main Title and Description */}
      <div className="space-y-2">
        <h1 className="text-[65px] lg:text-[72px] font-bold text-white leading-[78px]">
          {service.title}
        </h1>
        <p className=" text-[20px] leading-[36px]">
          <span className='text-white '>{service.description}</span>
        </p>
      </div>

      {/* Service Video - Autoplay */}
      <div className="relative rounded-2xl overflow-hidden bg-gray-800">
        <video
          className="w-full h-[400px] object-cover"
          autoPlay
          loop
          muted
          playsInline
        >
          <source src={videoSrc} type="video/mp4" /> //backend se video aana  postman
          {/* Fallback content if video fails to load */}
          {/* <div className="w-full h-[400px] bg-gradient-to-br from-gray-700 to-gray-900 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-black"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <p className="text-gray-400 text-lg">{serviceDetail.title}</p>
            </div>
          </div> */}
        </video>
      </div>

      {/* Strategy Section */}
      <div className="space-y-6">
        <h2 className="text-[48px] font-bold text-white">
          Our Strategy
        </h2>
        <p className="text-white opacity-[70%] text-[20px] leading-[36px]">
          {service.serviceDescription}
        </p>

        {/* Strategy Points */}
        <div className="space-y-4">
          {service.importantPoints.map((point: string, index: number) => (
            <div key={index} className="flex items-start gap-6 justify-start space-x-3">
              <div className="text-xl flex items-center justify-center text-white text-center rounded-full"><FaCheck className=''/></div>
              <span className="text-white text-[20px] leading-[38px]">{point}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Before Contacting Section - Accordion */}
      <div className="w-full space-y-6">
        <h2 className="w-[70%] text-[48px] leading-[56px] font-bold text-white">
          How to Get Started
        </h2>

        <div className="space-y-4 border-t py-3">
          {service.questionsAnswers.map((qa, index) => (
            <div key={qa._id} className="border-b border-[#FFFFFF33]">
              {/* Accordion Header */}
              <button
                onClick={() => toggleAccordion(index)}
                className="w-full flex items-center justify-between py-4 text-left hover:bg-gray-800/30 transition-colors rounded-lg px-2"
              >
                <div className="flex items-center space-x-4">
                  <span className="text-white font-semibold text-[24px] leading-[45px]">
                    {qa.order}. {qa.question}
                  </span>
                </div>
                <div className={`transform transition-transform duration-300 ${
                  openAccordion === index ? 'rotate-180' : ''
                }`}>
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </button>

              {/* Accordion Content */}
              <div className={`overflow-hidden transition-all duration-300 ${
                openAccordion === index ? 'max-h-96 opacity-100 pb-4' : 'max-h-0 opacity-0'
              }`}>
                <div className="px-2">
                  <p className="text-gray-300 text-[20px]  leading-[36px]">
                    {qa.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
