import { notFound } from 'next/navigation';
import { getServiceById } from '@/api/services/services_api';
import ServiceDetailsHero from '../../components/ServiceDetailsHero';
import ServiceDetailsSidebar from '../../components/ServiceDetailsSidebar';
import Link from 'next/link';
import ContactUsHome from '@/app/_components/common/contactusHome';

// Utility function to encode string to URL-safe format
const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]/g, '')
  );
};

interface ServiceDetailsProps {
  params: {
    id: string;
    name: string;
  };
}

export default async function ServiceDetailsPage({ params }: ServiceDetailsProps) {
  const { id } = params;

  let service;
  try {
    service = await getServiceById(id);
  } catch (error) {
    console.error('Error loading service:', error);
    notFound();
  }

  if (!service) {
    notFound();
  }

  return (
    <div className="w-full text-white">
      {/* Breadcrumb */}
      <div className=" py-6 px-4 sm:px-6 lg:px-8">
        <div className="max-w-[90%] mx-auto">
          <nav>
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link href="/services" className="text-gray-400 hover:text-white transition-colors">
                  Services
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-white font-medium">{service.title}</li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full mx-auto py-16 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center ">
        <div className=" max-w-[90%] w-full flex flex-row items-start justify-between gap-12">
         <div className='w-[75%] flex flex-col gap-5'>
           {/* Left Content - Service Details */}
          <div className="">
            <ServiceDetailsHero service={service} />
          </div>
         </div>

          {/* Right Sidebar - Service List */}
          <div className='w-[25%]'>
            <ServiceDetailsSidebar currentServiceId={service._id} />
          </div>
        </div>
      </div>


      <div className=''>
        <ContactUsHome/>
      </div>


    </div>
  );
}

// Generate static params for better performance (optional)
export async function generateStaticParams() {
  try {
    const { getAllServices } = await import('@/api/services/services_api');
    const services = await getAllServices();
    return services.map((service) => ({
      id: service._id, // Using _id instead of id
      name: encodeUrlString(service.title),
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ServiceDetailsProps) {
  try {
    const service = await getServiceById(params.id);

    return {
      title: `${service.title} | AgKraft Services`,
      description: service.description,
    };
  } catch (error) {
    return {
      title: 'Service Not Found',
      description: 'The requested service could not be found.',
    };
  }
}
