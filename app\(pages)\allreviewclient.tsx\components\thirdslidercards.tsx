"use client"
import { useState } from 'react';
import doublemark from "@/public/new assests/news icons/heroicons/doubleaxlimationmark.svg";
import Image from "next/image";
import { IoIosArrowRoundForward } from "react-icons/io";
import { IoIosArrowRoundBack } from "react-icons/io";
import gulshan from '@/public/new assests/news icons/heroicons/aboutus/gulshan.png';
import kunal from '@/public/new assests/news icons/heroicons/aboutus/kunal.png';
import aryan from '@/public/new assests/news icons/heroicons/aboutus/aryan.png';

export default function ThirdSliderCards() {
  const [currentSlide, setCurrentSlide] = useState(6); // Start from middle for infinite scroll

  const testimonials = [
    {
      quote: 'Game-changer! Boosted efficiency, simplified tasks, and saved time. Highly recommended!',
      author: '<PERSON>',
      location: 'USA',
      image: gulshan,
    },
    {
      quote: 'One should not hesitate to ask for the unlikely as they might think.',
      author: '<PERSON>',
      location: 'Spain',
      image: kunal,
    },
    {
      quote: 'Quick solutions coupled with great performance—a recommendation that\'s unequivocal!',
      author: '<PERSON>',
      location: 'UK',
      image: aryan,
    },
    {
      quote: 'Professional team with great attention to detail. Exceeded our expectations!',
      author: '<PERSON> <PERSON>',
      location: 'Canada',
      image: gulshan,
    },
    {
      quote: 'Quick solutions coupled with great performance—a recommendation that\'s unequivocal!',
      author: 'gulshan Smith',
      location: 'UK',
      image: aryan,
    },
    {
      quote: 'Professional team with great attention to detail. Exceeded our expectations!',
      author: 'kunal Wilson',
      location: 'Canada',
      image: gulshan,
    },
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => prev + 1); // Always move forward - no loop back
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => prev - 1); // Always move backward - no loop back
  };

  return (
    <div className="w-full mt-10 text-white">
      <div className="max-w-full ">
        <div className=" flex items-center w-full justify-center">
          {/* Left side content */}
          <div className="flex w-[40%] justif-center px-[5rem] items-start  flex-col">
            <div className="mb-6 ">
              <span className="text-[#FF640F] text-[18px] leading-[32px] font-medium tracking-[6%] uppercase">
                TESTIMONIALS
              </span>
            </div>
            <h2 className="text-[60px] md:text-[72px] font-bold leading-[72px] tracking-[-2px] mb-12">
              1.2k+ Clients<br />
              Love us
            </h2>

            {/* Navigation arrows */}
            <div className="flex space-x-4">
              <button
                onClick={prevSlide}
                className="w-[100px] h-[100px] hover:bg-orange-500 text-white hover:text-black rounded-full flex items-center justify-center border border-white transition-colors duration-200"
              >
                <IoIosArrowRoundBack className='text-[50px]' />
              </button>
              <button
                onClick={nextSlide}
                className="w-[100px] h-[100px] border-2 border-white text-white rounded-full flex items-center justify-center hover:bg-white hover:text-black transition-colors duration-200"
              >
                <IoIosArrowRoundForward className='text-[50px]' />
              </button>
            </div>
          </div>

          {/* Right side testimonial cards */}
          <div className="flex w-[60%] relative overflow-hidden">
            {/* Sliding container */}
            <div
              className="flex transition-transform duration-700 ease-in-out"
              style={{
                transform: `translateX(-${currentSlide * 320}px)`, // 320px = card width + gap
              }}
            >
              {/* Create very large array for truly infinite loop */}
              {Array.from({ length: testimonials.length * 10 }, (_, index) => {
                const testimonial = testimonials[index % testimonials.length];



                return (
                  <div
                    key={index}
                    className="flex-shrink-0 mr-8 transition-all m-8 duration-700 ease-in-out hover:scale-105"
                    style={{
                      width: '402px', // Fixed width for each card
                    }}
                  >
                    <div className="bg-[#504e4e66] hover:bg-[#FFFFFF33] cursor-pointer rounded-2xl p-8  h-[30rem] flex flex-col justify-between">
                      {/* Quote marks */}
                      <div className="mb-6">
                        <Image
                          src={doublemark}
                          alt="Quote marks"
                          width={32}
                          height={24}
                          className="w-[28px] h-[18px]"
                        />
                      </div>

                      {/* Testimonial text */}
                      <p className="text-white text-[28px] leading-[40px] mb-8 flex-grow">
                        {testimonial.quote}
                      </p>

                      {/* Author info */}
                      <div className="flex items-center">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.author}
                          className="rounded-full mr-4 w-[65px] h-[65px]"
                        />
                        <div>
                          <div className="text-white font-semibold text-[24px] leading-[30px] tracking-[-0.3px]">
                            {testimonial.author}
                          </div>
                          <div className="text-gray-400 text-[20px] leading-[30px] ">
                            {testimonial.location}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
