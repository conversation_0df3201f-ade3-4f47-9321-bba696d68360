"use client"

import React, { useState, useEffect } from 'react'
import Image from "next/image";
import Link from "next/link";
import { FaTwitter, FaLinkedinIn, FaInstagram } from 'react-icons/fa'
import { getAllTeamMembers, transformTeamMemberToCard, TeamCardData } from '@/api/team/team_api';

const AboutOwners: React.FC = () => {
  const [founders, setFounders] = useState<TeamCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFounders = async () => {
      try {
        setLoading(true);
        const teamData = await getAllTeamMembers();
        const transformedTeam = teamData.map(transformTeamMemberToCard);

        // Filter only founders and limit to 3
        const foundersOnly = transformedTeam
          .filter(member => member.role.toLowerCase().includes('founder'))
          .slice(0, 3); // Limit to 3 founders only

        setFounders(foundersOnly);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to load founders');
        console.error('Error loading founders:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFounders();
  }, []);

  if (loading) {
    return (
      <div className="w-full py-16 px-4 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-white">Loading Founders...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-16 px-4 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-16 px-4 flex flex-col justify-center items-center">
      <div className="w-[90%]  text-center flex flex-col justify-center items-center">
        <h2 className="text-[74px] leading-[74px] md:text-5xl font-bold text-white mb-4">
          Meet Our Team
        </h2>
        <p className="w-[68%] text-white opacity-[70%] text-[24px] leading-[37px] mb-12">
          Our founders Gulshan Kumar, Aryan Verma, Kunal Verma met while leading ENGINEERING.
          We try to Build Your Dreams.
        </p>

        <div className="mb-8 w-full flex flex-row justify-end">
          <Link
            href="/team"
            className="border border-white hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-20">
          {founders.map((member) => (
            <div key={member.id} className="flex flex-col gap-y-6 items-center">
              <div className="relative group cursor-pointer w-[435px] h-[500px] rounded-lg overflow-hidden mb-4">
                <Image
                  src={member.image}
                  alt={member.name}
                  width={435}
                  height={500}
                  className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                />

                {/* Social Media Icons Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-in-out flex items-center justify-center">
                  <div className="flex gap-6 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 ease-out">
                    {member.socialMedia.facebook && (
                      <a
                        href={member.socialMedia.facebook}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-[55px] h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaInstagram className="text-[25px]" />
                      </a>
                    )}
                    {member.socialMedia.twitter && (
                      <a
                        href={member.socialMedia.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-[55px] h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaTwitter className="text-[25px]" />
                      </a>
                    )}
                    {member.socialMedia.linkedin && (
                      <a
                        href={member.socialMedia.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-[55px] h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaLinkedinIn className="text-[25px]" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
             <div className="flex flex-col">
               <h3 className="text-[24px] font-semibold text-white">{member.name}</h3>
              <p className="text-white opacity-[50%] text-[17px]">{member.role}</p>
             </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AboutOwners;