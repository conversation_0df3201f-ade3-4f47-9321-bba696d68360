export default function ServiceDetailsLoading() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header Skeleton */}
      <div className="relative bg-gradient-to-br from-gray-900 to-black py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb Skeleton */}
          <div className="mb-8 flex items-center space-x-2">
            <div className="h-4 bg-gray-700 rounded w-12 animate-pulse"></div>
            <div className="h-4 bg-gray-700 rounded w-1 animate-pulse"></div>
            <div className="h-4 bg-gray-700 rounded w-16 animate-pulse"></div>
            <div className="h-4 bg-gray-700 rounded w-1 animate-pulse"></div>
            <div className="h-4 bg-gray-700 rounded w-32 animate-pulse"></div>
          </div>
          
          {/* Service Header Skeleton */}
          <div className="flex flex-col lg:flex-row items-start lg:items-center gap-8">
            {/* Icon Skeleton */}
            <div className="w-24 h-24 bg-gray-700 rounded-full animate-pulse flex-shrink-0"></div>
            
            {/* Title and Description Skeleton */}
            <div className="flex-1 space-y-4">
              <div className="h-12 bg-gray-700 rounded w-3/4 animate-pulse"></div>
              <div className="h-6 bg-gray-700 rounded w-full animate-pulse"></div>
              <div className="h-6 bg-gray-700 rounded w-2/3 animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Main Content Skeleton */}
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content Skeleton */}
          <div className="lg:col-span-2 space-y-12">
            {/* Service Overview Skeleton */}
            <section className="space-y-4">
              <div className="h-8 bg-gray-700 rounded w-48 animate-pulse"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-700 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-700 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-700 rounded w-3/4 animate-pulse"></div>
              </div>
            </section>
            
            {/* Key Features Skeleton */}
            <section className="space-y-4">
              <div className="h-8 bg-gray-700 rounded w-32 animate-pulse"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-gray-700 rounded-full mt-3 animate-pulse"></div>
                    <div className="h-4 bg-gray-700 rounded w-40 animate-pulse"></div>
                  </div>
                ))}
              </div>
            </section>
            
            {/* Process Skeleton */}
            <section className="space-y-6">
              <div className="h-8 bg-gray-700 rounded w-32 animate-pulse"></div>
              {[...Array(4)].map((_, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="h-12 bg-gray-700 rounded w-12 animate-pulse"></div>
                  <div className="space-y-2 flex-1">
                    <div className="h-6 bg-gray-700 rounded w-48 animate-pulse"></div>
                    <div className="h-4 bg-gray-700 rounded w-full animate-pulse"></div>
                  </div>
                </div>
              ))}
            </section>
          </div>
          
          {/* Sidebar Skeleton */}
          <div className="space-y-8">
            {/* Contact Card Skeleton */}
            <div className="bg-gray-900 rounded-2xl p-6 space-y-4">
              <div className="h-6 bg-gray-700 rounded w-32 animate-pulse"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-700 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-700 rounded w-3/4 animate-pulse"></div>
              </div>
              <div className="h-12 bg-gray-700 rounded animate-pulse"></div>
            </div>
            
            {/* Related Services Skeleton */}
            <div className="bg-gray-900 rounded-2xl p-6 space-y-4">
              <div className="h-6 bg-gray-700 rounded w-32 animate-pulse"></div>
              <div className="space-y-3">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3">
                    <div className="w-10 h-10 bg-gray-700 rounded-full animate-pulse"></div>
                    <div className="h-4 bg-gray-700 rounded w-32 animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
