"use client"
import React, { useState, useEffect } from 'react'
import { getAllBlogs, getBlogCategories, getRecentBlogs, BlogData } from '@/api/blogs/blogs_api'
import BlogCard from './BlogCard'
import BlogSidebar from './BlogSidebar'
import BlogPagination from './BlogPagination'

export default function BlogCards() {
  const [blogs, setBlogs] = useState<BlogData[]>([])
  const [categories, setCategories] = useState<Array<{ name: string; count: number }>>([])
  const [recentBlogs, setRecentBlogs] = useState<BlogData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Fetch blogs data
  useEffect(() => {
    fetchBlogs()
    fetchCategories()
    fetchRecentBlogs()
  }, [currentPage, searchQuery, selectedCategory])

  const fetchBlogs = async () => {
    try {
      setLoading(true)
      const response = await getAllBlogs({
        page: currentPage,
        limit: 6,
        search: searchQuery || undefined,
        category: selectedCategory || undefined
      })
      
      if (response.status) {
        setBlogs(response.data.data)
        setTotalPages(response.data.totalPages)
      }
    } catch (error) {
      console.error('Error fetching blogs:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories()
      if (response.status) {
        setCategories(response.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const fetchRecentBlogs = async () => {
    try {
      const response = await getRecentBlogs(3)
      if (response.status) {
        setRecentBlogs(response.data.data)
      }
    } catch (error) {
      console.error('Error fetching recent blogs:', error)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchBlogs()
  }

  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
  }

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? '' : category)
    setCurrentPage(1)
  }

  const handleKeywordClick = (keyword: string) => {
    setSearchQuery(keyword.replace(/[\[\]"]/g, ''))
    setCurrentPage(1)
    fetchBlogs()
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  // Get all unique keywords from blogs
  const allKeywords = blogs
    .flatMap(blog => blog.keywords)
    .filter((keyword, index, arr) => arr.indexOf(keyword) === index)

  return (
    <div className="text-white w-full flex justify-center py-20 bg-black">
      <div className="w-[95%] max-w-[1400px] flex gap-8">
        {/* Left Side - Blog Cards */}
        <div className="flex-1">
          {loading ? (
            <div className="text-center py-20">
              <div className="text-white">Loading blogs...</div>
            </div>
          ) : (
            <>
              {/* Blog Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
                {blogs.map((blog) => (
                  <BlogCard key={blog._id} blog={blog} />
                ))}
              </div>

              {/* Pagination */}
              <BlogPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </>
          )}
        </div>

        {/* Right Sidebar */}
        <BlogSidebar
          categories={categories}
          recentBlogs={recentBlogs}
          allKeywords={allKeywords}
          searchQuery={searchQuery}
          selectedCategory={selectedCategory}
          onSearchChange={handleSearchChange}
          onSearchSubmit={handleSearch}
          onCategoryFilter={handleCategoryFilter}
          onKeywordClick={handleKeywordClick}
        />
      </div>
    </div>
  )
}
